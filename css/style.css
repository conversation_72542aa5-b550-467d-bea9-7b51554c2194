/* Custom styles for Log Management System */

/* Login Page Styles */
.login-page {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    min-height: 100vh;
}

.login-bg {
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 1000 1000"><polygon fill="%23ffffff" fill-opacity="0.1" points="0,1000 1000,0 1000,1000"/></svg>') no-repeat center center;
    background-size: cover;
    position: relative;
}

.login-bg .overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.3);
}

.login-bg .content {
    position: relative;
    z-index: 1;
    padding: 2rem;
    display: flex;
    flex-direction: column;
    justify-content: center;
    height: 100%;
}

.login-form-container {
    background: white;
    padding: 2rem;
    border-radius: 10px;
    box-shadow: 0 15px 35px rgba(0, 0, 0, 0.1);
    max-width: 400px;
    width: 100%;
}

/* Navigation Styles */
.navbar-brand {
    font-weight: 600;
}

.navbar-nav .nav-link {
    font-weight: 500;
    transition: color 0.3s ease;
}

.navbar-nav .nav-link:hover {
    color: rgba(255, 255, 255, 0.8) !important;
}

/* Card Styles */
.card {
    border: none;
    border-radius: 10px;
    transition: transform 0.2s ease, box-shadow 0.2s ease;
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15) !important;
}

.card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 10px 10px 0 0 !important;
    border: none;
}

/* Section headings - make all headings white */
.card-header h6,
.card-header h5,
.card-header h4,
.card-header h3,
.card-header h2,
.card-header h1 {
    color: white !important;
}

/* Main page headings */
.container-fluid h1,
.container-fluid h2,
.container-fluid h3 {
    color: white !important;
}

/* Card titles in headers */
.card-header .card-title,
.card-header .font-weight-bold {
    color: white !important;
}

/* Modal titles */
.modal-header .modal-title {
    color: white !important;
}

/* Preserve existing color classes for functionality */
.text-primary {
    color: #007bff !important;
}

.text-success {
    color: #28a745 !important;
}

.text-warning {
    color: #ffc107 !important;
}

.text-danger {
    color: #dc3545 !important;
}

.text-info {
    color: #17a2b8 !important;
}

.text-muted {
    color: #6c757d !important;
}

/* Stats Cards */
.border-left-primary {
    border-left: 4px solid #007bff !important;
}

.border-left-success {
    border-left: 4px solid #28a745 !important;
}

.border-left-warning {
    border-left: 4px solid #ffc107 !important;
}

.border-left-info {
    border-left: 4px solid #17a2b8 !important;
}

/* Button Styles */
.btn {
    border-radius: 8px;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-primary {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border: none;
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
    transform: translateY(-1px);
    box-shadow: 0 5px 15px rgba(102, 126, 234, 0.4);
}

.btn-outline-primary {
    border-color: #667eea;
    color: #667eea;
}

.btn-outline-primary:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: transparent;
}

/* Form Styles */
.form-control, .form-select {
    border-radius: 8px;
    border: 2px solid #e9ecef;
    transition: border-color 0.3s ease, box-shadow 0.3s ease;
}

.form-control:focus, .form-select:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.input-group-text {
    border-radius: 8px 0 0 8px;
    border: 2px solid #e9ecef;
    border-right: none;
    background: #f8f9fa;
}

.input-group .form-control {
    border-left: none;
    border-radius: 0 8px 8px 0;
}

/* Table Styles */
.table {
    border-radius: 8px;
    overflow: hidden;
}

.table thead th {
    background: #f8f9fa;
    border-bottom: 2px solid #dee2e6;
    font-weight: 600;
    color: #495057;
}

.table-hover tbody tr:hover {
    background-color: rgba(102, 126, 234, 0.05);
}

/* Badge Styles */
.badge {
    font-weight: 500;
    padding: 0.5em 0.75em;
    border-radius: 6px;
}

/* Progress Bar */
.progress {
    border-radius: 10px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

/* Modal Styles */
.modal-content {
    border-radius: 15px;
    border: none;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
}

.modal-header {
    border-bottom: 1px solid #e9ecef;
    border-radius: 15px 15px 0 0;
}

.modal-footer {
    border-top: 1px solid #e9ecef;
    border-radius: 0 0 15px 15px;
}

/* Alert Styles */
.alert {
    border-radius: 8px;
    border: none;
    font-weight: 500;
}

/* Pagination Styles */
.pagination .page-link {
    border-radius: 6px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #667eea;
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

/* Project Cards */
.project-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 20px;
    overflow: hidden;
    background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
    box-shadow: 0 4px 15px rgba(0,0,0,0.08);
    position: relative;
}

.project-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 4px;
    background: linear-gradient(90deg, #667eea 0%, #764ba2 50%, #f093fb 100%);
}

.project-card:hover {
    transform: translateY(-8px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.12);
}

.project-card .card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    border-radius: 20px 20px 0 0 !important;
    border: none;
    padding: 1.25rem 1.5rem;
    position: relative;
}

.project-card .card-header::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: linear-gradient(90deg, transparent 0%, rgba(255,255,255,0.3) 50%, transparent 100%);
}

.project-card .card-body {
    padding: 1.75rem;
    background: #ffffff;
}

.project-card .card-title {
    font-size: 1.1rem;
    font-weight: 600;
    color: #2c3e50;
    margin-bottom: 0.5rem;
}

.project-card .card-text {
    color: #6c757d;
    font-size: 0.9rem;
    line-height: 1.5;
    margin-bottom: 1.25rem;
}

.project-card .badge {
    font-size: 0.7rem;
    padding: 0.4rem 0.8rem;
    border-radius: 20px;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.project-card .progress {
    height: 6px;
    border-radius: 10px;
    background-color: #e9ecef;
    overflow: hidden;
}

.project-card .progress-bar {
    border-radius: 10px;
    transition: width 0.6s ease;
}

.project-card .dropdown-toggle {
    border: 1px solid rgba(255,255,255,0.3);
    background: rgba(255,255,255,0.1);
    color: white;
    border-radius: 8px;
    padding: 0.25rem 0.5rem;
    transition: all 0.3s ease;
}

.project-card .dropdown-toggle:hover {
    background: rgba(255,255,255,0.2);
    border-color: rgba(255,255,255,0.5);
}

.project-card .dropdown-menu {
    border: none;
    border-radius: 12px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
    padding: 0.5rem;
}

.project-card .dropdown-item {
    border-radius: 8px;
    padding: 0.5rem 0.75rem;
    font-size: 0.85rem;
    transition: all 0.2s ease;
}

.project-card .dropdown-item:hover {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    transform: translateX(3px);
}

/* Project stats styling */
.project-stats {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-radius: 12px;
    padding: 1rem;
    margin: 1rem 0;
}

.project-stats .stat-item {
    text-align: center;
    padding: 0.5rem;
}

.project-stats .stat-value {
    font-size: 1.25rem;
    font-weight: 700;
    color: #2c3e50;
    display: block;
}

.project-stats .stat-label {
    font-size: 0.75rem;
    color: #6c757d;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    margin-top: 0.25rem;
}

/* Status badges */
.badge.bg-success {
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%) !important;
}

.badge.bg-secondary {
    background: linear-gradient(135deg, #6c757d 0%, #495057 100%) !important;
}

.badge.bg-info {
    background: linear-gradient(135deg, #17a2b8 0%, #20c997 100%) !important;
}

/* Progress bar colors */
.progress-bar.bg-success {
    background: linear-gradient(90deg, #28a745 0%, #20c997 100%) !important;
}

.progress-bar.bg-warning {
    background: linear-gradient(90deg, #ffc107 0%, #fd7e14 100%) !important;
}

.progress-bar.bg-danger {
    background: linear-gradient(90deg, #dc3545 0%, #e83e8c 100%) !important;
}

/* Enhanced project card animations */
.project-card {
    animation: fadeInUp 0.6s ease-out;
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Card hover effects */
.project-card:hover .card-header {
    background: linear-gradient(135deg, #5a6fd8 0%, #6a4c93 100%);
}

.project-card:hover .stat-value {
    color: #667eea;
    transform: scale(1.05);
    transition: all 0.3s ease;
}

/* Status indicator animations */
.project-card .fas.fa-circle {
    animation: pulse 2s infinite;
}

@keyframes pulse {
    0% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
    100% {
        opacity: 1;
    }
}

/* Enhanced dropdown animations */
.project-card .dropdown-menu {
    animation: slideDown 0.3s ease-out;
}

@keyframes slideDown {
    from {
        opacity: 0;
        transform: translateY(-10px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Professional shadows and depth */
.project-card {
    box-shadow: 
        0 4px 15px rgba(0,0,0,0.08),
        0 1px 3px rgba(0,0,0,0.1);
}

.project-card:hover {
    box-shadow: 
        0 20px 40px rgba(0,0,0,0.12),
        0 8px 16px rgba(0,0,0,0.1);
}

/* Enhanced typography */
.project-card .card-title {
    background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

/* Professional spacing */
.project-card .card-body > *:last-child {
    margin-bottom: 0;
}

/* Project type checkboxes styling */
.form-check {
    margin-bottom: 0.5rem;
    padding-left: 1.5rem;
}

.form-check-input {
    width: 1.1rem;
    height: 1.1rem;
    margin-top: 0.1rem;
    border: 2px solid #dee2e6;
    border-radius: 4px;
    transition: all 0.2s ease;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-input:focus {
    border-color: #667eea;
    box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25);
}

.form-check-label {
    font-size: 0.9rem;
    color: #495057;
    cursor: pointer;
    transition: color 0.2s ease;
}

.form-check-label:hover {
    color: #667eea;
}

.form-check-input:checked + .form-check-label {
    color: #667eea;
    font-weight: 500;
}

/* Multiple project types display */
.project-type-icons {
    display: flex;
    align-items: center;
    gap: 0.25rem;
    flex-wrap: wrap;
}

.project-type-icons i {
    font-size: 0.9rem;
    padding: 0.2rem;
    border-radius: 4px;
    background: rgba(102, 126, 234, 0.1);
    color: #667eea;
}

/* Favorite button styling */
.btn-outline-warning {
    border-color: #ffc107;
    color: #ffc107;
    transition: all 0.3s ease;
}

.btn-outline-warning:hover {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
    transform: scale(1.1);
}

.btn-warning {
    background-color: #ffc107;
    border-color: #ffc107;
    color: #212529;
    transition: all 0.3s ease;
}

.btn-warning:hover {
    background-color: #e0a800;
    border-color: #d39e00;
    transform: scale(1.1);
}

/* Pagination styling */
.pagination .page-link {
    border-radius: 8px;
    margin: 0 2px;
    border: 1px solid #dee2e6;
    color: #667eea;
    transition: all 0.3s ease;
}

.pagination .page-link:hover {
    background-color: #667eea;
    border-color: #667eea;
    color: white;
    transform: translateY(-1px);
}

.pagination .page-item.active .page-link {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
    color: white;
}

.pagination .page-item.disabled .page-link {
    color: #6c757d;
    background-color: #fff;
    border-color: #dee2e6;
}

/* Favorite section styling */
#favoriteSection .card {
    border: 2px solid #ffc107;
    background: linear-gradient(145deg, #fffbf0 0%, #fff8dc 100%);
}

#favoriteSection .card-header {
    background: linear-gradient(135deg, #ffc107 0%, #ff8c00 100%);
    color: #212529;
}

/* Project Details Page */
.content-section {
    animation: fadeIn 0.3s ease-in;
}

.list-group-item {
    border: none;
    border-radius: 0;
    transition: all 0.3s ease;
}

.list-group-item:hover {
    background-color: #f8f9fa;
    transform: translateX(5px);
}

.list-group-item.active {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    border-color: #667eea;
}

.list-group-item.active:hover {
    transform: translateX(5px);
}

/* Breadcrumb styling */
.breadcrumb-item a {
    color: #667eea;
    text-decoration: none;
}

.breadcrumb-item a:hover {
    color: #5a6fd8;
}

/* Chart containers */
canvas {
    max-height: 300px;
}

/* Logs table styling */
.table th {
    background-color: #f8f9fa;
    border-top: none;
    font-weight: 600;
    color: #495057;
}

.table td {
    vertical-align: middle;
}

/* Report filters */
.form-label {
    font-weight: 500;
    color: #495057;
}

/* Project card hover effect for clickable cards */
.project-card[style*="cursor: pointer"]:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 25px 50px rgba(0,0,0,0.15);
}

.project-status {
    position: absolute;
    top: 1rem;
    right: 1rem;
    z-index: 1;
}

/* Log Level Colors */
.log-level-debug { color: #6c757d; }
.log-level-info { color: #17a2b8; }
.log-level-warn { color: #ffc107; }
.log-level-error { color: #dc3545; }
.log-level-fatal { color: #721c24; }

/* Responsive Design */
@media (max-width: 768px) {
    .login-form-container {
        margin: 1rem;
        padding: 1.5rem;
    }
    
    .card-body {
        padding: 1rem;
    }
    
    .table-responsive {
        font-size: 0.9rem;
    }
}

/* Animation Classes */
.fade-in {
    animation: fadeIn 0.5s ease-in;
}

@keyframes fadeIn {
    from { opacity: 0; transform: translateY(20px); }
    to { opacity: 1; transform: translateY(0); }
}

.slide-in {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { transform: translateX(-100%); }
    to { transform: translateX(0); }
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb {
    background: #c1c1c1;
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: #a8a8a8;
}

/* Loading Spinner */
.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #667eea;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Password Strength Indicator */
.password-strength-weak { background-color: #dc3545; }
.password-strength-fair { background-color: #ffc107; }
.password-strength-good { background-color: #17a2b8; }
.password-strength-strong { background-color: #28a745; }

/* Chart Container */
.chart-container {
    position: relative;
    height: 300px;
    width: 100%;
}

/* Profile Picture */
.profile-picture {
    border: 4px solid #fff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
}

/* Utility Classes */
.text-xs {
    font-size: 0.75rem;
}

.font-weight-bold {
    font-weight: 600;
}

.text-gray-300 {
    color: #dee2e6 !important;
}

.text-gray-800 {
    color: #343a40 !important;
}

/* Dark mode support */
@media (prefers-color-scheme: dark) {
    .card {
        background-color: #2d3748;
        color: #e2e8f0;
    }
    
    .table {
        color: #e2e8f0;
    }
    
    .table thead th {
        background-color: #4a5568;
        color: #e2e8f0;
    }
    
    .form-control, .form-select {
        background-color: #4a5568;
        border-color: #718096;
        color: #e2e8f0;
    }
    
    .form-control:focus, .form-select:focus {
        background-color: #4a5568;
        color: #e2e8f0;
    }
}

/* Project Details Tabs: Smaller and Left-Aligned */
.nav-tabs {
    border-bottom: 2px solid #dee2e6;
    justify-content: flex-start !important;
}

.nav-tabs .nav-link {
    border: none;
    border-bottom: 3px solid transparent;
    color: #6c757d;
    font-weight: 500;
    padding: 6px 16px;
    font-size: 0.95rem;
    min-width: 90px;
    max-width: 140px;
    text-align: left;
    transition: all 0.3s ease;
}

.nav-tabs .nav-link:hover {
    border-color: transparent;
    color: #007bff;
    background-color: rgba(0, 123, 255, 0.05);
}

.nav-tabs .nav-link.active {
    color: #007bff;
    background-color: transparent;
    border-bottom: 3px solid #007bff;
}

.nav-tabs .nav-link .badge {
    font-size: 0.75em;
    padding: 0.25em 0.5em;
}

@media (max-width: 768px) {
    .nav-tabs .nav-link {
        padding: 5px 10px;
        font-size: 0.85rem;
        min-width: 70px;
        max-width: 100px;
    }
}

/* Tab Content Animation */
.tab-pane {
    animation: slideIn 0.3s ease-out;
}

@keyframes slideIn {
    from { 
        opacity: 0; 
        transform: translateY(20px); 
    }
    to { 
        opacity: 1; 
        transform: translateY(0); 
    }
}

/* Enhanced Card Styles for Project Details */
.card {
    border: none;
    box-shadow: 0 2px 10px rgba(0,0,0,0.08);
    border-radius: 12px;
    transition: all 0.3s ease;
}

.card:hover {
    box-shadow: 0 4px 20px rgba(0,0,0,0.12);
    transform: translateY(-2px);
}

.card-header {
    background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
    border-bottom: 1px solid #dee2e6;
    border-radius: 12px 12px 0 0 !important;
    padding: 15px 20px;
}

.card-body {
    padding: 20px;
}

/* Metrics Display */
.metrics-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.metric-card {
    background: linear-gradient(135deg, #ffffff 0%, #f8f9fa 100%);
    border: 1px solid #e9ecef;
    border-radius: 10px;
    padding: 20px;
    text-align: center;
    transition: all 0.3s ease;
}

.metric-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

.metric-value {
    font-size: 2rem;
    font-weight: bold;
    margin-bottom: 5px;
}

.metric-label {
    color: #6c757d;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Progress Bars */
.progress {
    height: 8px;
    border-radius: 4px;
    background-color: #e9ecef;
}

.progress-bar {
    border-radius: 4px;
    transition: width 0.6s ease;
}

/* Responsive Design for Tabs */
@media (max-width: 768px) {
    .nav-tabs .nav-link {
        padding: 10px 15px;
        font-size: 0.9rem;
    }
    
    .nav-tabs .nav-link .badge {
        font-size: 0.7em;
        padding: 0.2em 0.4em;
    }
    
    .card-body {
        padding: 15px;
    }
    
    .metrics-grid {
        grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
        gap: 15px;
    }
    
    .metric-card {
        padding: 15px;
    }
    
    .metric-value {
        font-size: 1.5rem;
    }
}

#projectTitle {
  color: #111 !important;
  font-weight: bold;
}

.project-badges {
  background: #f8f9fa;
  border-radius: 2rem;
  padding: 0.25rem 0.75rem;
  box-shadow: 0 2px 8px rgba(0,0,0,0.03);
  margin-bottom: 0.5rem;
  display: flex;
  gap: 0.5rem;
}

.badge.status-badge,
.badge.log-badge {
  border-radius: 2rem;
  font-size: 0.95rem;
  font-weight: 600;
  padding: 0.4em 1.1em;
  display: flex;
  align-items: center;
  box-shadow: 0 1px 4px rgba(0,0,0,0.04);
  letter-spacing: 0.02em;
}

.badge.status-badge {
  background: linear-gradient(90deg, #2196f3 0%, #21cbf3 100%);
  color: #fff;
  border: none;
}

.badge.log-badge.bg-success {
  background: linear-gradient(90deg, #11998e 0%, #38ef7d 100%);
  color: #fff;
  border: none;
}

.badge.log-badge.bg-secondary {
  background: #e0e0e0;
  color: #555;
  border: none;
}

/* Role Badge Styles */
.badge.role-badge {
  font-size: 0.75rem;
  font-weight: 500;
  padding: 0.35em 0.65em;
  border-radius: 0.375rem;
}

.badge.role-badge.bg-secondary {
  background: linear-gradient(135deg, #6c757d 0%, #5a6268 100%) !important;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.badge.role-badge.bg-warning {
  background: linear-gradient(135deg, #ffc107 0%, #e0a800 100%) !important;
  color: #212529 !important;
  border: 1px solid rgba(0, 0, 0, 0.1);
}

.badge.role-badge.bg-danger {
  background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
  color: white;
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Invited Users List Styles */
.list-group-item.invited-user-item {
  border-left: 4px solid #007bff;
  transition: all 0.3s ease;
}

.list-group-item.invited-user-item:hover {
  background-color: #f8f9fa;
  transform: translateX(2px);
}

.invited-user-info .user-name {
  font-weight: 600;
  color: #2c3e50;
}

.invited-user-info .user-email {
  color: #6c757d;
  font-size: 0.875rem;
}