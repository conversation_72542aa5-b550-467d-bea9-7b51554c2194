<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Management System - Profile</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-shield-alt me-2"></i>Log Management System
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.html">
                            <i class="fas fa-project-diagram me-1"></i>Projects
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>John Doe
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item active" href="profile.html">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="change-password.html">
                                <i class="fas fa-key me-2"></i>Change Password
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logoutBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row">
            <div class="col-12">
                <h1 class="h3 mb-4">User Profile</h1>
            </div>
        </div>

        <div class="row">
            <!-- Profile Information -->
            <div class="col-lg-8">
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Personal Information</h6>
                    </div>
                    <div class="card-body">
                        <form id="profileForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="firstName" class="form-label">First Name *</label>
                                    <input type="text" class="form-control" id="firstName" name="firstName" value="John" required>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="lastName" class="form-label">Last Name *</label>
                                    <input type="text" class="form-control" id="lastName" name="lastName" value="Doe" required>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="email" class="form-label">Email Address *</label>
                                <input type="email" class="form-control" id="email" name="email" value="<EMAIL>" required>
                            </div>
                            
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="phone" class="form-label">Phone Number</label>
                                    <input type="tel" class="form-control" id="phone" name="phone" value="+****************">
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="jobTitle" class="form-label">Job Title</label>
                                    <input type="text" class="form-control" id="jobTitle" name="jobTitle" value="System Administrator">
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="department" class="form-label">Department</label>
                                <select class="form-select" id="department" name="department">
                                    <option value="it">IT Department</option>
                                    <option value="development">Development</option>
                                    <option value="operations">Operations</option>
                                    <option value="support">Support</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <label for="bio" class="form-label">Bio</label>
                                <textarea class="form-control" id="bio" name="bio" rows="3" placeholder="Tell us about yourself...">Experienced system administrator with expertise in log management and monitoring systems.</textarea>
                            </div>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-2"></i>Update Profile
                            </button>
                        </form>
                    </div>
                </div>

                <!-- Preferences -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Preferences</h6>
                    </div>
                    <div class="card-body">
                        <form id="preferencesForm">
                            <div class="row">
                                <div class="col-md-6 mb-3">
                                    <label for="timezone" class="form-label">Timezone</label>
                                    <select class="form-select" id="timezone" name="timezone">
                                        <option value="UTC">UTC</option>
                                        <option value="EST">Eastern Time</option>
                                        <option value="CST">Central Time</option>
                                        <option value="MST">Mountain Time</option>
                                        <option value="PST">Pacific Time</option>
                                    </select>
                                </div>
                                <div class="col-md-6 mb-3">
                                    <label for="language" class="form-label">Language</label>
                                    <select class="form-select" id="language" name="language">
                                        <option value="en">English</option>
                                        <option value="es">Spanish</option>
                                        <option value="fr">French</option>
                                        <option value="de">German</option>
                                    </select>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <label for="dateFormat" class="form-label">Date Format</label>
                                <select class="form-select" id="dateFormat" name="dateFormat">
                                    <option value="MM/DD/YYYY">MM/DD/YYYY</option>
                                    <option value="DD/MM/YYYY">DD/MM/YYYY</option>
                                    <option value="YYYY-MM-DD">YYYY-MM-DD</option>
                                </select>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="emailNotifications" name="emailNotifications" checked>
                                    <label class="form-check-label" for="emailNotifications">
                                        Receive email notifications
                                    </label>
                                </div>
                            </div>
                            
                            <div class="mb-3">
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" id="dashboardAlerts" name="dashboardAlerts" checked>
                                    <label class="form-check-label" for="dashboardAlerts">
                                        Show alerts on dashboard
                                    </label>
                                </div>
                            </div>
                            
                            <button type="submit" class="btn btn-outline-primary">
                                <i class="fas fa-cog me-2"></i>Save Preferences
                            </button>
                        </form>
                    </div>
                </div>
            </div>

            <!-- Profile Sidebar -->
            <div class="col-lg-4">
                <!-- Profile Picture -->
                <div class="card shadow mb-4">
                    <div class="card-body text-center">
                        <div class="position-relative d-inline-block mb-3">
                            <img src="https://via.placeholder.com/150x150/007bff/ffffff?text=JD" 
                                 class="rounded-circle" 
                                 alt="Profile Picture" 
                                 id="profilePicture"
                                 style="width: 150px; height: 150px; object-fit: cover;">
                            <button class="btn btn-sm btn-primary position-absolute bottom-0 end-0" 
                                    style="border-radius: 50%; width: 40px; height: 40px;"
                                    onclick="document.getElementById('profilePictureInput').click();">
                                <i class="fas fa-camera"></i>
                            </button>
                            <input type="file" id="profilePictureInput" accept="image/*" style="display: none;">
                        </div>
                        <h5 class="card-title">John Doe</h5>
                        <p class="text-muted">System Administrator</p>
                        <p class="text-muted mb-0">Member since January 2023</p>
                    </div>
                </div>

                <!-- Account Information -->
                <div class="card shadow mb-4">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Account Information</h6>
                    </div>
                    <div class="card-body">
                        <div class="mb-3">
                            <strong>Username:</strong>
                            <p class="text-muted mb-0">johndoe</p>
                        </div>
                        <div class="mb-3">
                            <strong>Role:</strong>
                            <p class="text-muted mb-0">Administrator</p>
                        </div>
                        <div class="mb-3">
                            <strong>Last Login:</strong>
                            <p class="text-muted mb-0">Today at 9:30 AM</p>
                        </div>
                        <div class="mb-3">
                            <strong>Status:</strong>
                            <span class="badge bg-success">Active</span>
                        </div>
                    </div>
                </div>

                <!-- Quick Actions -->
                <div class="card shadow">
                    <div class="card-header py-3">
                        <h6 class="m-0 font-weight-bold text-primary">Quick Actions</h6>
                    </div>
                    <div class="card-body">
                        <div class="d-grid gap-2">
                            <a href="change-password.html" class="btn btn-outline-primary">
                                <i class="fas fa-key me-2"></i>Change Password
                            </a>
                            <button class="btn btn-outline-warning" id="exportDataBtn">
                                <i class="fas fa-download me-2"></i>Export My Data
                            </button>
                            <button class="btn btn-outline-danger" id="deleteAccountBtn">
                                <i class="fas fa-trash me-2"></i>Delete Account
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Delete Account Modal -->
    <div class="modal fade" id="deleteAccountModal" tabindex="-1">
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title text-danger">Delete Account</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="alert alert-warning">
                        <i class="fas fa-exclamation-triangle me-2"></i>
                        <strong>Warning:</strong> This action cannot be undone. All your data will be permanently deleted.
                    </div>
                    <p>Are you sure you want to delete your account? This will:</p>
                    <ul>
                        <li>Permanently delete your profile</li>
                        <li>Remove all your preferences</li>
                        <li>Delete all associated data</li>
                    </ul>
                    <div class="mb-3">
                        <label for="confirmDelete" class="form-label">Type "DELETE" to confirm</label>
                        <input type="text" class="form-control" id="confirmDelete" placeholder="DELETE">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="button" class="btn btn-danger" id="confirmDeleteBtn" disabled>Delete Account</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/profile.js"></script>
</body>
</html> 