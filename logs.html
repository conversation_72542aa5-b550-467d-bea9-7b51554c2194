<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Management System - Logs</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-shield-alt me-2"></i>Log Management System
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.html">
                            <i class="fas fa-project-diagram me-1"></i>Projects
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="logs.html">
                            <i class="fas fa-file-alt me-1"></i>Logs
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="reports.html">
                            <i class="fas fa-chart-bar me-1"></i>Reports
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>John Doe
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.html">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="change-password.html">
                                <i class="fas fa-key me-2"></i>Change Password
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logoutBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col-12">
                <h1 class="h3">Logs</h1>
            </div>
        </div>

        <!-- Filters and Search -->
        <div class="card shadow mb-4">
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <label for="projectFilter" class="form-label">Project</label>
                        <select class="form-select" id="projectFilter">
                            <option value="">All Projects</option>
                            <option value="ecommerce">E-commerce App</option>
                            <option value="api-gateway">API Gateway</option>
                            <option value="user-service">User Service</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="levelFilter" class="form-label">Log Level</label>
                        <select class="form-select" id="levelFilter">
                            <option value="">All Levels</option>
                            <option value="debug">Debug</option>
                            <option value="info">Info</option>
                            <option value="warn">Warning</option>
                            <option value="error">Error</option>
                            <option value="fatal">Fatal</option>
                        </select>
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="dateFrom" class="form-label">From Date</label>
                        <input type="datetime-local" class="form-control" id="dateFrom">
                    </div>
                    <div class="col-md-3 mb-3">
                        <label for="dateTo" class="form-label">To Date</label>
                        <input type="datetime-local" class="form-control" id="dateTo">
                    </div>
                </div>
                <div class="row">
                    <div class="col-md-8 mb-3">
                        <label for="searchLogs" class="form-label">Search</label>
                        <input type="text" class="form-control" id="searchLogs" placeholder="Search in log messages...">
                    </div>
                    <div class="col-md-4 mb-3 d-flex align-items-end">
                        <div class="d-grid gap-2 d-md-flex">
                            <button class="btn btn-primary" id="searchBtn">
                                <i class="fas fa-search me-2"></i>Search
                            </button>
                            <button class="btn btn-outline-secondary" id="clearFiltersBtn">
                                <i class="fas fa-times me-2"></i>Clear
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Log Statistics -->
        <div class="row mb-4">
            <div class="col-md-2">
                <div class="card bg-primary text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Total</h5>
                        <h3 id="totalLogs">2,847</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-success text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Info</h5>
                        <h3 id="infoLogs">1,234</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-warning text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Warning</h5>
                        <h3 id="warningLogs">456</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-danger text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Error</h5>
                        <h3 id="errorLogs">157</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-info text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Debug</h5>
                        <h3 id="debugLogs">1,000</h3>
                    </div>
                </div>
            </div>
            <div class="col-md-2">
                <div class="card bg-dark text-white">
                    <div class="card-body text-center">
                        <h5 class="card-title">Fatal</h5>
                        <h3 id="fatalLogs">0</h3>
                    </div>
                </div>
            </div>
        </div>

        <!-- Logs Table -->
        <div class="card shadow">
            <div class="card-header py-3 d-flex justify-content-between align-items-center">
                <h6 class="m-0 font-weight-bold text-primary">Log Entries</h6>
                <div class="btn-group" role="group">
                    <button type="button" class="btn btn-outline-primary btn-sm" id="refreshBtn">
                        <i class="fas fa-sync-alt me-1"></i>Refresh
                    </button>
                    <button type="button" class="btn btn-outline-primary btn-sm" id="exportBtn">
                        <i class="fas fa-download me-1"></i>Export
                    </button>
                </div>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table table-hover" id="logsTable">
                        <thead>
                            <tr>
                                <th>Timestamp</th>
                                <th>Project</th>
                                <th>Level</th>
                                <th>Message</th>
                                <th>User</th>
                                <th>IP Address</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody id="logsTableBody">
                            <!-- Log entries will be dynamically loaded here -->
                        </tbody>
                    </table>
                </div>
                
                <!-- Pagination -->
                <nav aria-label="Logs pagination">
                    <ul class="pagination justify-content-center" id="logsPagination">
                        <li class="page-item disabled">
                            <a class="page-link" href="#" tabindex="-1">Previous</a>
                        </li>
                        <li class="page-item active"><a class="page-link" href="#">1</a></li>
                        <li class="page-item"><a class="page-link" href="#">2</a></li>
                        <li class="page-item"><a class="page-link" href="#">3</a></li>
                        <li class="page-item">
                            <a class="page-link" href="#">Next</a>
                        </li>
                    </ul>
                </nav>
            </div>
        </div>
    </div>

    <!-- Log Detail Modal -->
    <div class="modal fade" id="logDetailModal" tabindex="-1">
        <div class="modal-dialog modal-xl">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Log Details</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <div class="modal-body">
                    <div class="row">
                        <div class="col-md-6">
                            <h6>Basic Information</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Timestamp:</strong></td><td id="detailTimestamp"></td></tr>
                                <tr><td><strong>Project:</strong></td><td id="detailProject"></td></tr>
                                <tr><td><strong>Level:</strong></td><td id="detailLevel"></td></tr>
                                <tr><td><strong>User:</strong></td><td id="detailUser"></td></tr>
                                <tr><td><strong>IP Address:</strong></td><td id="detailIP"></td></tr>
                            </table>
                        </div>
                        <div class="col-md-6">
                            <h6>Request Information</h6>
                            <table class="table table-sm">
                                <tr><td><strong>Method:</strong></td><td id="detailMethod"></td></tr>
                                <tr><td><strong>URL:</strong></td><td id="detailURL"></td></tr>
                                <tr><td><strong>Status Code:</strong></td><td id="detailStatusCode"></td></tr>
                                <tr><td><strong>Response Time:</strong></td><td id="detailResponseTime"></td></tr>
                            </table>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-12">
                            <h6>Message</h6>
                            <div class="bg-light p-3 rounded" id="detailMessage"></div>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>Request Headers</h6>
                            <pre class="bg-light p-3 rounded" id="detailRequestHeaders"></pre>
                        </div>
                        <div class="col-md-6">
                            <h6>Response Headers</h6>
                            <pre class="bg-light p-3 rounded" id="detailResponseHeaders"></pre>
                        </div>
                    </div>
                    
                    <div class="row mt-3">
                        <div class="col-md-6">
                            <h6>Request Body</h6>
                            <pre class="bg-light p-3 rounded" id="detailRequestBody"></pre>
                        </div>
                        <div class="col-md-6">
                            <h6>Response Body</h6>
                            <pre class="bg-light p-3 rounded" id="detailResponseBody"></pre>
                        </div>
                    </div>
                    
                    <div class="row mt-3" id="stackTraceSection" style="display: none;">
                        <div class="col-12">
                            <h6>Stack Trace</h6>
                            <pre class="bg-light p-3 rounded" id="detailStackTrace"></pre>
                        </div>
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/logs.js"></script>
</body>
</html> 