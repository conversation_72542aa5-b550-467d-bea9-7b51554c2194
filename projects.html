<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Management System - Projects</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-shield-alt me-2"></i>Log Management System
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link active" href="projects.html">
                            <i class="fas fa-project-diagram me-1"></i>Projects
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="invited-users.html">
                            <i class="fas fa-users me-1"></i>Invited Users
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>John Doe
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.html">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="change-password.html">
                                <i class="fas fa-key me-2"></i>Change Password
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logoutBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <div class="row mb-4">
            <div class="col-md-6">
                <h1 class="h3">Projects</h1>
            </div>
            <div class="col-md-6 text-end">
                <button class="btn btn-primary" data-bs-toggle="modal" data-bs-target="#addProjectModal">
                    <i class="fas fa-plus me-2"></i>Add New Project
                </button>
            </div>
        </div>

        <!-- Search and Filter -->
        <div class="row mb-4">
            <div class="col-md-8">
                <div class="input-group">
                    <span class="input-group-text"><i class="fas fa-search"></i></span>
                    <input type="text" class="form-control" id="searchProjects" placeholder="Search projects...">
                </div>
            </div>
            <div class="col-md-4">
                <select class="form-select" id="statusFilter">
                    <option value="">All Status</option>
                    <option value="active">Active</option>
                    <option value="inactive">Inactive</option>
                </select>
            </div>
        </div>

        <!-- Favorite Projects Section -->
        <div class="row mb-4" id="favoriteSection" style="display: none;">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h6 class="mb-0">
                            <i class="fas fa-star text-warning me-2"></i>Favorite Projects
                        </h6>
                    </div>
                    <div class="card-body">
                        <div class="row" id="favoriteProjectsGrid">
                            <!-- Favorite project cards will be loaded here -->
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- All Projects Section -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h6 class="mb-0">
                            <i class="fas fa-project-diagram me-2"></i>All Projects
                        </h6>
                        <div class="d-flex align-items-center">
                            <span class="text-muted me-3">Showing <span id="showingInfo">1-6</span> of <span id="totalProjects">0</span> projects</span>
                        </div>
                    </div>
                    <div class="card-body">
                        <div class="row" id="projectsGrid">
                            <!-- Project cards will be dynamically loaded here -->
                        </div>
                        
                        <!-- Pagination -->
                        <div class="row mt-4">
                            <div class="col-12">
                                <nav aria-label="Projects pagination">
                                    <ul class="pagination justify-content-center" id="pagination">
                                        <!-- Pagination will be generated here -->
                                    </ul>
                                </nav>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Add Project Modal -->
    <div class="modal fade" id="addProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Add New Project</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="addProjectForm">
                    <div class="modal-body">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="projectName" class="form-label">Project Name *</label>
                                <input type="text" class="form-control" id="projectName" name="projectName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="projectDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="projectDescription" name="projectDescription" rows="2"></textarea>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="projectType" class="form-label">Project Type</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Mobile" id="typeMobile" name="projectType">
                                    <label class="form-check-label" for="typeMobile">
                                        <i class="fas fa-mobile-alt me-1"></i>Mobile
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Web" id="typeWeb" name="projectType">
                                    <label class="form-check-label" for="typeWeb">
                                        <i class="fas fa-globe me-1"></i>Web
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Desktop" id="typeDesktop" name="projectType">
                                    <label class="form-check-label" for="typeDesktop">
                                        <i class="fas fa-desktop me-1"></i>Desktop
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="projectStatus" class="form-label">Status</label>
                                <select class="form-select" id="projectStatus" name="projectStatus">
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="teamMembers" class="form-label">Assigned Team Members</label>
                                <input type="text" class="form-control" id="teamMembers" name="teamMembers" placeholder="e.g., John Doe, Jane Smith">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="logRetention" class="form-label">Log Retention Period</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="logRetentionValue" name="logRetentionValue" value="30" min="1">
                                    <select class="form-select" id="logRetentionUnit" name="logRetentionUnit">
                                        <option value="hrs">Hours</option>
                                        <option value="days" selected>Days</option>
                                        <option value="months">Months</option>
                                        <option value="years">Years</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="totalUniqueApis" class="form-label">Total Unique APIs</label>
                                <input type="number" class="form-control" id="totalUniqueApis" name="totalUniqueApis" value="0" min="0">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="totalRequests" class="form-label">Total No of Requests</label>
                                <input type="number" class="form-control" id="totalRequests" name="totalRequests" value="0" min="0">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="totalUsers" class="form-label">Total No of Users</label>
                                <input type="number" class="form-control" id="totalUsers" name="totalUsers" value="0" min="0">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="apiSuccessRate" class="form-label">API Success Rate (%)</label>
                                <input type="number" class="form-control" id="apiSuccessRate" name="apiSuccessRate" value="95" min="0" max="100" step="0.1">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Create Project</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Edit Project Modal -->
    <div class="modal fade" id="editProjectModal" tabindex="-1">
        <div class="modal-dialog modal-lg">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">Edit Project</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
                </div>
                <form id="editProjectForm">
                    <div class="modal-body">
                        <input type="hidden" id="editProjectId" name="projectId">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editProjectName" class="form-label">Project Name *</label>
                                <input type="text" class="form-control" id="editProjectName" name="projectName" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editProjectDescription" class="form-label">Description</label>
                                <textarea class="form-control" id="editProjectDescription" name="projectDescription" rows="2"></textarea>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editProjectType" class="form-label">Project Type</label>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Mobile" id="editTypeMobile" name="projectType">
                                    <label class="form-check-label" for="editTypeMobile">
                                        <i class="fas fa-mobile-alt me-1"></i>Mobile
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Web" id="editTypeWeb" name="projectType">
                                    <label class="form-check-label" for="editTypeWeb">
                                        <i class="fas fa-globe me-1"></i>Web
                                    </label>
                                </div>
                                <div class="form-check">
                                    <input class="form-check-input" type="checkbox" value="Desktop" id="editTypeDesktop" name="projectType">
                                    <label class="form-check-label" for="editTypeDesktop">
                                        <i class="fas fa-desktop me-1"></i>Desktop
                                    </label>
                                </div>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editProjectStatus" class="form-label">Status</label>
                                <select class="form-select" id="editProjectStatus" name="projectStatus">
                                    <option value="Active">Active</option>
                                    <option value="Inactive">Inactive</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editTeamMembers" class="form-label">Assigned Team Members</label>
                                <input type="text" class="form-control" id="editTeamMembers" name="teamMembers" placeholder="e.g., John Doe, Jane Smith">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editLogRetention" class="form-label">Log Retention Period</label>
                                <div class="input-group">
                                    <input type="number" class="form-control" id="editLogRetentionValue" name="logRetentionValue" min="1">
                                    <select class="form-select" id="editLogRetentionUnit" name="logRetentionUnit">
                                        <option value="hrs">Hours</option>
                                        <option value="days">Days</option>
                                        <option value="months">Months</option>
                                        <option value="years">Years</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editTotalUniqueApis" class="form-label">Total Unique APIs</label>
                                <input type="number" class="form-control" id="editTotalUniqueApis" name="totalUniqueApis" min="0">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editTotalRequests" class="form-label">Total No of Requests</label>
                                <input type="number" class="form-control" id="editTotalRequests" name="totalRequests" min="0">
                            </div>
                        </div>
                        
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label for="editTotalUsers" class="form-label">Total No of Users</label>
                                <input type="number" class="form-control" id="editTotalUsers" name="totalUsers" min="0">
                            </div>
                            <div class="col-md-6 mb-3">
                                <label for="editApiSuccessRate" class="form-label">API Success Rate (%)</label>
                                <input type="number" class="form-control" id="editApiSuccessRate" name="apiSuccessRate" min="0" max="100" step="0.1">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                        <button type="submit" class="btn btn-primary">Update Project</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="js/projects.js"></script>
</body>
</html> 