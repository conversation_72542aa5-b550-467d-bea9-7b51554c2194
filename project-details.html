<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Log Management System - Project Details</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="css/style.css" rel="stylesheet">
</head>
<body>
    <!-- Navigation -->
    <nav class="navbar navbar-expand-lg navbar-dark bg-primary">
        <div class="container-fluid">
            <a class="navbar-brand" href="dashboard.html">
                <i class="fas fa-shield-alt me-2"></i>Log Management System
            </a>
            
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbarNav">
                <span class="navbar-toggler-icon"></span>
            </button>
            
            <div class="collapse navbar-collapse" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link" href="dashboard.html">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="projects.html">
                            <i class="fas fa-project-diagram me-1"></i>Projects
                        </a>
                    </li>
                </ul>
                
                <ul class="navbar-nav">
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-circle me-1"></i>John Doe
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li><a class="dropdown-item" href="profile.html">
                                <i class="fas fa-user me-2"></i>Profile
                            </a></li>
                            <li><a class="dropdown-item" href="change-password.html">
                                <i class="fas fa-key me-2"></i>Change Password
                            </a></li>
                            <li><hr class="dropdown-divider"></li>
                            <li><a class="dropdown-item" href="#" id="logoutBtn">
                                <i class="fas fa-sign-out-alt me-2"></i>Logout
                            </a></li>
                        </ul>
                    </li>
                </ul>
            </div>
        </div>
    </nav>

    <!-- Main Content -->
    <div class="container-fluid mt-4">
        <!-- Project Header -->
        <div class="row mb-4">
            <div class="col-12">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h1 class="h3 mb-0 fw-bold text-dark" id="projectTitle">Project Details</h1>
                    </div>
                    <div>
                        <button class="btn btn-outline-primary me-2" onclick="goBack()">
                            <i class="fas fa-arrow-left me-2"></i>Back to Projects
                        </button>
                        <button class="btn btn-primary" onclick="editProject()">
                            <i class="fas fa-edit me-2"></i>Edit Project
                        </button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Top Navigation Tabs -->
        <div class="row mb-2">
            <div class="col-12">
                <div class="card">
                    <div class="card-body p-0">
                        <ul class="nav nav-tabs" id="projectTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <button class="nav-link active" id="overview-tab" data-bs-toggle="tab" data-bs-target="#overview-section" type="button" role="tab">
                                    <i class="fas fa-info-circle me-2"></i>Overview
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="logs-tab" data-bs-toggle="tab" data-bs-target="#logs-section" type="button" role="tab">
                                    <i class="fas fa-file-alt me-2"></i>Logs
                                    <span class="badge bg-primary ms-2" id="logsCount">0</span>
                                </button>
                            </li>
                            <li class="nav-item" role="presentation">
                                <button class="nav-link" id="reports-tab" data-bs-toggle="tab" data-bs-target="#reports-section" type="button" role="tab">
                                    <i class="fas fa-chart-bar me-2"></i>Reports
                                </button>
                            </li>
                        </ul>
                    </div>
                </div>
            </div>
        </div>

        <!-- Content Area -->
        <div class="row">
            <div class="col-12">
                <div class="tab-content" id="projectTabContent">
                    <!-- Overview Section -->
                    <div class="tab-pane fade show active" id="overview-section" role="tabpanel">
                        <div class="card">
                            <div class="card-body">
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="mb-2"><strong>ID:</strong> <span id="overviewProjectId"></span></div>
                                        <h5 id="overviewProjectName">Project Name</h5>
                                        <div class="mb-3">
                                            <strong>Status:</strong>
                                            <span class="badge bg-success ms-2" id="overviewStatus">Active</span>
                                        </div>
                                        <div class="mb-3">
                                            <strong>Logs:</strong>
                                            <span class="badge ms-2" id="overviewLogEnabled">Log Enabled</span>
                                        </div>
                                        <div class="mb-3">
                                            <strong>Project Type:</strong>
                                            <span class="badge bg-info ms-2" id="overviewType">Web, Mobile</span>
                                        </div>
                                        <div class="mb-3">
                                            <strong>Log Retention:</strong>
                                            <p class="mb-0" id="overviewRetention">30 days</p>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="row">
                                            <div class="col-6 mb-3">
                                                <div class="text-center">
                                                    <div class="h4 text-primary" id="overviewApis">25</div>
                                                    <small class="text-muted">Unique APIs</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="text-center">
                                                    <div class="h4 text-success" id="overviewRequests">1.2M</div>
                                                    <small class="text-muted">Total Requests</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="text-center">
                                                    <div class="h4 text-info" id="overviewUsers">50K</div>
                                                    <small class="text-muted">Total Users</small>
                                                </div>
                                            </div>
                                            <div class="col-6 mb-3">
                                                <div class="text-center">
                                                    <div class="h4 text-warning" id="overviewSuccess">98.5%</div>
                                                    <small class="text-muted">Success Rate</small>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="mt-3">
                                            <strong>API Success Rate:</strong>
                                            <div class="progress mt-2">
                                                <div class="progress-bar bg-success" id="overviewProgress" style="width: 98.5%"></div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Logs Section -->
                    <div class="tab-pane fade" id="logs-section" role="tabpanel">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-file-alt me-2"></i>Project Logs
                                </h6>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary me-2" onclick="exportLogs()">
                                        <i class="fas fa-download me-1"></i>Export
                                    </button>
                                    <button class="btn btn-sm btn-primary" onclick="refreshLogs()">
                                        <i class="fas fa-sync-alt me-1"></i>Refresh
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Log Filters -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <label for="logLevelFilter" class="form-label">Log Level</label>
                                        <select class="form-select" id="logLevelFilter">
                                            <option value="">All Levels</option>
                                            <option value="ERROR">Error</option>
                                            <option value="WARN">Warning</option>
                                            <option value="INFO">Info</option>
                                            <option value="DEBUG">Debug</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="logDateFilter" class="form-label">Date Range</label>
                                        <select class="form-select" id="logDateFilter">
                                            <option value="1h">Last Hour</option>
                                            <option value="24h">Last 24 Hours</option>
                                            <option value="7d" selected>Last 7 Days</option>
                                            <option value="30d">Last 30 Days</option>
                                        </select>
                                    </div>
                                    <div class="col-md-4">
                                        <label for="logSearch" class="form-label">Search</label>
                                        <input type="text" class="form-control" id="logSearch" placeholder="Search logs...">
                                    </div>
                                    <div class="col-md-2">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary w-100" onclick="filterLogs()">
                                            <i class="fas fa-search me-1"></i>Filter
                                        </button>
                                    </div>
                                </div>

                                <!-- Logs Table -->
                                <div class="table-responsive">
                                    <table class="table table-hover">
                                        <thead>
                                            <tr>
                                                <th>Timestamp</th>
                                                <th>Level</th>
                                                <th>Message</th>
                                                <th>Source</th>
                                                <th>Actions</th>
                                            </tr>
                                        </thead>
                                        <tbody id="logsTableBody">
                                            <!-- Logs will be loaded here -->
                                        </tbody>
                                    </table>
                                </div>

                                <!-- Logs Pagination -->
                                <div class="row mt-4">
                                    <div class="col-12">
                                        <nav aria-label="Logs pagination">
                                            <ul class="pagination justify-content-center" id="logsPagination">
                                                <!-- Pagination will be generated here -->
                                            </ul>
                                        </nav>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Reports Section -->
                    <div class="tab-pane fade" id="reports-section" role="tabpanel">
                        <div class="card">
                            <div class="card-header d-flex justify-content-between align-items-center">
                                <h6 class="mb-0">
                                    <i class="fas fa-chart-bar me-2"></i>Project Reports
                                </h6>
                                <div>
                                    <button class="btn btn-sm btn-outline-primary me-2" onclick="exportReport()">
                                        <i class="fas fa-download me-1"></i>Export Report
                                    </button>
                                    <button class="btn btn-sm btn-primary" onclick="generateReport()">
                                        <i class="fas fa-chart-line me-1"></i>Generate Report
                                    </button>
                                </div>
                            </div>
                            <div class="card-body">
                                <!-- Report Filters -->
                                <div class="row mb-4">
                                    <div class="col-md-3">
                                        <label for="reportType" class="form-label">Report Type</label>
                                        <select class="form-select" id="reportType">
                                            <option value="performance">Performance Report</option>
                                            <option value="errors">Error Analysis</option>
                                            <option value="usage">Usage Statistics</option>
                                            <option value="summary">Summary Report</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="reportPeriod" class="form-label">Time Period</label>
                                        <select class="form-select" id="reportPeriod">
                                            <option value="7d">Last 7 Days</option>
                                            <option value="30d" selected>Last 30 Days</option>
                                            <option value="90d">Last 90 Days</option>
                                            <option value="1y">Last Year</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label for="reportFormat" class="form-label">Format</label>
                                        <select class="form-select" id="reportFormat">
                                            <option value="pdf">PDF</option>
                                            <option value="excel">Excel</option>
                                            <option value="csv">CSV</option>
                                        </select>
                                    </div>
                                    <div class="col-md-3">
                                        <label class="form-label">&nbsp;</label>
                                        <button class="btn btn-primary w-100" onclick="generateReport()">
                                            <i class="fas fa-chart-line me-1"></i>Generate
                                        </button>
                                    </div>
                                </div>

                                <!-- Charts Section -->
                                <div class="row">
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">Request Volume</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="requestChart" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-6 mb-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">Error Distribution</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="errorChart" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                </div>

                                <div class="row">
                                    <div class="col-md-8 mb-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">Performance Trends</h6>
                                            </div>
                                            <div class="card-body">
                                                <canvas id="performanceChart" width="400" height="200"></canvas>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-md-4 mb-4">
                                        <div class="card">
                                            <div class="card-header">
                                                <h6 class="mb-0">Key Metrics</h6>
                                            </div>
                                            <div class="card-body">
                                                <div class="mb-3">
                                                    <div class="d-flex justify-content-between">
                                                        <span>Average Response Time</span>
                                                        <strong id="avgResponseTime">245ms</strong>
                                                    </div>
                                                    <div class="progress mt-1">
                                                        <div class="progress-bar bg-success" style="width: 85%"></div>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="d-flex justify-content-between">
                                                        <span>Success Rate</span>
                                                        <strong id="successRate">98.5%</strong>
                                                    </div>
                                                    <div class="progress mt-1">
                                                        <div class="progress-bar bg-success" style="width: 98.5%"></div>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="d-flex justify-content-between">
                                                        <span>Error Rate</span>
                                                        <strong id="errorRate">1.5%</strong>
                                                    </div>
                                                    <div class="progress mt-1">
                                                        <div class="progress-bar bg-danger" style="width: 1.5%"></div>
                                                    </div>
                                                </div>
                                                <div class="mb-3">
                                                    <div class="d-flex justify-content-between">
                                                        <span>Active Users</span>
                                                        <strong id="activeUsers">12,847</strong>
                                                    </div>
                                                    <div class="progress mt-1">
                                                        <div class="progress-bar bg-info" style="width: 75%"></div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Invite Users Section -->
        <div class="card mt-4">
            <div class="card-header">
                <h6 class="mb-0"><i class="fas fa-user-plus me-2"></i>Invite Users</h6>
            </div>
            <div class="card-body">
                <form id="inviteUserForm" class="row g-2 align-items-end">
                    <div class="col-md-3">
                        <label for="inviteName" class="form-label mb-1">Name</label>
                        <input type="text" class="form-control" id="inviteName" placeholder="Enter name" required>
                    </div>
                    <div class="col-md-3">
                        <label for="inviteEmail" class="form-label mb-1">Email</label>
                        <input type="email" class="form-control" id="inviteEmail" placeholder="Enter email" required>
                    </div>
                    <div class="col-md-3">
                        <label for="inviteRole" class="form-label mb-1">
                            <i class="fas fa-user-tag me-1"></i>Role
                        </label>
                        <select class="form-select" id="inviteRole" required>
                            <option value="">Select role</option>
                            <option value="user">👤 User</option>
                            <option value="admin">⚙️ Admin</option>
                            <option value="super_admin">👑 Super Admin</option>
                        </select>
                    </div>
                    <div class="col-md-3">
                        <button type="submit" class="btn btn-primary w-100"><i class="fas fa-paper-plane me-1"></i>Invite</button>
                    </div>
                </form>
                <div class="mt-4">
                    <div class="d-flex justify-content-between align-items-center mb-3">
                        <h6 class="mb-0">Invited Users</h6>
                        <span class="badge bg-primary" id="invitedUsersCount">0</span>
                    </div>
                    <ul class="list-group" id="invitedUsersList">
                        <!-- Invited users will be listed here -->
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Scripts -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="js/project-details.js"></script>
</body>
</html> 