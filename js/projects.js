// Global variables for pagination
let allProjects = [];
let currentPage = 1;
const projectsPerPage = 6;

// Projects page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuthentication();
    
    // Initialize projects
    initializeProjects();
    
    // Setup event listeners
    setupEventListeners();
});

// Check if user is authenticated
function checkAuthentication() {
    const isAuthenticated = localStorage.getItem('isLoggedIn') === 'true' || sessionStorage.getItem('isAuthenticated') === 'true';
    if (!isAuthenticated) {
        window.location.href = 'index.html';
        return;
    }
}

// Initialize projects
function initializeProjects() {
    loadProjects();
    setupSearchAndFilter();
    setupPagination();
}

// Load projects data
function loadProjects() {
    // Mock projects data - replace with actual API call
    allProjects = [
        {
            id: 1,
            name: 'E-commerce Platform',
            description: 'Complete e-commerce solution with web and mobile apps',
            status: 'Active',
            teamMembers: '<PERSON>, <PERSON>, <PERSON>',
            projectType: 'Web, Mobile',
            logRetention: '30 days',
            totalUniqueApis: 25,
            totalRequests: 1250000,
            totalUsers: 50000,
            apiSuccessRate: 98.5,
            isFavorite: true,
            logEnabled: true
        },
        {
            id: 2,
            name: 'API Gateway Service',
            description: 'Central API gateway for microservices architecture',
            status: 'Active',
            teamMembers: 'Sarah Wilson, Tom Brown',
            projectType: 'Web',
            logRetention: '60 days',
            totalUniqueApis: 15,
            totalRequests: 2150000,
            totalUsers: 75000,
            apiSuccessRate: 99.2,
            isFavorite: false,
            logEnabled: true
        },
        {
            id: 3,
            name: 'User Management System',
            description: 'Comprehensive user management across all platforms',
            status: 'Active',
            teamMembers: 'Alex Chen, Lisa Davis',
            projectType: 'Web, Mobile, Desktop',
            logRetention: '45 days',
            totalUniqueApis: 8,
            totalRequests: 892000,
            totalUsers: 25000,
            apiSuccessRate: 97.8,
            isFavorite: true,
            logEnabled: true
        },
        {
            id: 4,
            name: 'Mobile Banking App',
            description: 'Cross-platform mobile banking application',
            status: 'Inactive',
            teamMembers: 'David Lee, Emma Wilson',
            projectType: 'Mobile',
            logRetention: '90 days',
            totalUniqueApis: 12,
            totalRequests: 567000,
            totalUsers: 15000,
            apiSuccessRate: 95.5,
            isFavorite: false,
            logEnabled: false
        },
        {
            id: 5,
            name: 'Data Analytics Suite',
            description: 'Enterprise analytics software for data analysis',
            status: 'Active',
            teamMembers: 'Robert Taylor, Maria Garcia',
            projectType: 'Desktop, Web',
            logRetention: '6 months',
            totalUniqueApis: 5,
            totalRequests: 320000,
            totalUsers: 8000,
            apiSuccessRate: 96.8,
            isFavorite: false,
            logEnabled: true
        },
        {
            id: 6,
            name: 'Cloud Storage Solution',
            description: 'Secure cloud storage and file management system',
            status: 'Active',
            teamMembers: 'Michael Brown, Lisa Wang',
            projectType: 'Web, Desktop',
            logRetention: '90 days',
            totalUniqueApis: 18,
            totalRequests: 890000,
            totalUsers: 35000,
            apiSuccessRate: 99.1,
            isFavorite: false,
            logEnabled: true
        },
        {
            id: 7,
            name: 'IoT Dashboard',
            description: 'Real-time IoT device monitoring and control',
            status: 'Active',
            teamMembers: 'David Chen, Sarah Kim',
            projectType: 'Web, Mobile',
            logRetention: '30 days',
            totalUniqueApis: 22,
            totalRequests: 1450000,
            totalUsers: 28000,
            apiSuccessRate: 97.3,
            isFavorite: false,
            logEnabled: true
        },
        {
            id: 8,
            name: 'AI Chatbot Platform',
            description: 'Intelligent chatbot platform with natural language processing',
            status: 'Active',
            teamMembers: 'Alex Johnson, Maria Rodriguez',
            projectType: 'Web, Mobile, Desktop',
            logRetention: '60 days',
            totalUniqueApis: 30,
            totalRequests: 2100000,
            totalUsers: 45000,
            apiSuccessRate: 98.7,
            isFavorite: false,
            logEnabled: true
        }
    ];
    
    displayProjects();
    displayFavoriteProjects();
    updatePagination();
}

// Display projects in grid with pagination
function displayProjects() {
    const projectsGrid = document.getElementById('projectsGrid');
    projectsGrid.innerHTML = '';
    
    const startIndex = (currentPage - 1) * projectsPerPage;
    const endIndex = startIndex + projectsPerPage;
    const projectsToShow = allProjects.slice(startIndex, endIndex);
    
    projectsToShow.forEach(project => {
        const projectCard = createProjectCard(project);
        projectsGrid.appendChild(projectCard);
    });
    
    updatePaginationInfo();
}

// Display favorite projects
function displayFavoriteProjects() {
    const favoriteProjectsGrid = document.getElementById('favoriteProjectsGrid');
    const favoriteSection = document.getElementById('favoriteSection');
    favoriteProjectsGrid.innerHTML = '';
    
    const favoriteProjects = allProjects.filter(project => project.isFavorite);
    
    if (favoriteProjects.length > 0) {
        favoriteSection.style.display = 'block';
        favoriteProjects.forEach(project => {
            const projectCard = createProjectCard(project);
            favoriteProjectsGrid.appendChild(projectCard);
        });
    } else {
        favoriteSection.style.display = 'none';
    }
}

// Create project card
function createProjectCard(project) {
    const col = document.createElement('div');
    col.className = 'col-lg-6 col-xl-4 mb-4';
    
    const statusClass = project.status === 'Active' ? 'bg-success' : 'bg-secondary';
    const typeIcons = getProjectTypeIcons(project.projectType);
    
    col.innerHTML = `
        <div class="card project-card h-100" style="cursor: pointer;">
            <div class="card-header d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <i class="fas fa-project-diagram me-2"></i>
                    <h6 class="mb-0 font-weight-bold">Project #${project.id}</h6>
                </div>
                <div class="dropdown" onclick="event.stopPropagation();">
                    <button class="btn btn-sm dropdown-toggle" type="button" data-bs-toggle="dropdown">
                        <i class="fas fa-ellipsis-v"></i>
                    </button>
                    <ul class="dropdown-menu">
                        <li><a class="dropdown-item" href="#" onclick="editProject(${project.id}); event.stopPropagation(); return false;">
                            <i class="fas fa-edit me-2"></i>Edit Project
                        </a></li>
                        <li><a class="dropdown-item" href="#" onclick="openProjectDetails(${project.id}); event.stopPropagation(); return false;">
                            <i class="fas fa-eye me-2"></i>View Details
                        </a></li>
                        <li><hr class="dropdown-divider"></li>
                        <li><a class="dropdown-item text-danger" href="#" onclick="deleteProject(${project.id}); event.stopPropagation(); return false;">
                            <i class="fas fa-trash me-2"></i>Delete Project
                        </a></li>
                    </ul>
                </div>
            </div>
            <div class="card-body">
                <div class="mb-3">
                    <h6 class="card-title">${project.name}</h6>
                </div>
                
                <div class="row mb-3">
                    <div class="col-6">
                        <div class="project-badges d-flex align-items-center gap-2 mb-2">
                            <span class="badge status-badge ${statusClass}"><i class="fas fa-bolt me-1"></i>${project.status}</span>
                            <span class="badge log-badge ${project.logEnabled ? 'bg-success' : 'bg-secondary'}">
                                <i class="fas ${project.logEnabled ? 'fa-check-circle' : 'fa-times-circle'} me-1"></i>
                                ${project.logEnabled ? 'Log Enabled' : 'Log Disabled'}
                            </span>
                        </div>
                        <div class="d-flex align-items-center flex-wrap">
                            <div class="project-type-icons me-2">
                                ${typeIcons.map(icon => `<i class="fas ${icon}"></i>`).join('')}
                            </div>
                            <span class="badge bg-info">${project.projectType}</span>
                        </div>
                    </div>
                    <div class="col-6">
                        <div class="text-end">
                            <button class="btn btn-sm ${project.isFavorite ? 'btn-warning' : 'btn-outline-warning'}" 
                                    onclick="toggleFavorite(${project.id}); event.stopPropagation(); return false;" title="${project.isFavorite ? 'Remove from favorites' : 'Add to favorites'}">
                                <i class="fas fa-star"></i>
                            </button>
                        </div>
                    </div>
                </div>
                
                <div class="project-stats mb-3">
                    <div class="row text-center">
                        <div class="col-4">
                            <div class="stat-item">
                                <span class="stat-value">${project.totalUniqueApis}</span>
                                <div class="stat-label">APIs</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <span class="stat-value">${(project.totalRequests / 1000).toFixed(1)}K</span>
                                <div class="stat-label">Requests</div>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="stat-item">
                                <span class="stat-value">${(project.totalUsers / 1000).toFixed(1)}K</span>
                                <div class="stat-label">Users</div>
                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="mb-3">
                    <div class="d-flex justify-content-between align-items-center mb-2">
                        <small class="text-muted">API Success Rate</small>
                        <small class="fw-bold ${project.apiSuccessRate >= 90 ? 'text-success' : project.apiSuccessRate >= 70 ? 'text-warning' : 'text-danger'}">${project.apiSuccessRate}%</small>
                    </div>
                    <div class="progress">
                        <div class="progress-bar ${project.apiSuccessRate >= 90 ? 'bg-success' : project.apiSuccessRate >= 70 ? 'bg-warning' : 'bg-danger'}" 
                             style="width: ${project.apiSuccessRate}%"></div>
                    </div>
                </div>
                
                <div class="row">
                    <div class="col-12">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <small class="text-muted d-block">Team Members</small>
                                <small class="fw-bold">${project.teamMembers}</small>
                            </div>
                            <div class="text-end">
                                <small class="text-muted d-block">Retention</small>
                                <small class="fw-bold">${project.logRetention}</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Attach click event to the card (excluding dropdown and favorite button)
    const card = col.querySelector('.card');
    card.addEventListener('click', function (e) {
        // Prevent click if dropdown or favorite button is clicked
        if (
            e.target.closest('.dropdown') ||
            e.target.closest('.btn-outline-warning') ||
            e.target.closest('.btn-warning')
        ) {
            return;
        }
        openProjectDetails(project.id);
    });

    return col;
}

// Get project type icons for multiple types
function getProjectTypeIcons(types) {
    const icons = {
        'Web': 'fas fa-globe',
        'Mobile': 'fas fa-mobile-alt',
        'Desktop': 'fas fa-desktop'
    };
    
    const typeArray = types.split(', ').map(type => type.trim());
    return typeArray.map(type => icons[type] || 'fas fa-cube');
}

// Format date
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString();
}

// Setup search and filter
function setupSearchAndFilter() {
    const searchInput = document.getElementById('searchProjects');
    const statusFilter = document.getElementById('statusFilter');
    
    searchInput.addEventListener('input', filterProjects);
    statusFilter.addEventListener('change', filterProjects);
}

// Setup pagination
function setupPagination() {
    updatePagination();
}

// Update pagination
function updatePagination() {
    const totalPages = Math.ceil(allProjects.length / projectsPerPage);
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    if (totalPages <= 1) {
        return;
    }
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>`;
    pagination.appendChild(prevLi);
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>`;
    pagination.appendChild(nextLi);
}

// Change page
function changePage(page) {
    const totalPages = Math.ceil(allProjects.length / projectsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        displayProjects();
        updatePagination();
    }
}

// Update pagination info
function updatePaginationInfo() {
    const totalProjects = allProjects.length;
    const startIndex = (currentPage - 1) * projectsPerPage + 1;
    const endIndex = Math.min(currentPage * projectsPerPage, totalProjects);
    
    document.getElementById('showingInfo').textContent = `${startIndex}-${endIndex}`;
    document.getElementById('totalProjects').textContent = totalProjects;
}

// Toggle favorite
function toggleFavorite(projectId) {
    const project = allProjects.find(p => p.id === projectId);
    if (project) {
        project.isFavorite = !project.isFavorite;
        displayProjects();
        displayFavoriteProjects();
        updatePagination();
        
        const message = project.isFavorite ? 'Added to favorites' : 'Removed from favorites';
        showAlert(message, 'success');
    }
}

// Open project details
function openProjectDetails(projectId) {
    window.location.href = `project-details.html?id=${projectId}`;
}

// Filter projects
function filterProjects() {
    const searchTerm = document.getElementById('searchProjects').value.toLowerCase();
    const statusFilter = document.getElementById('statusFilter').value;
    
    // Filter the projects array
    const filteredProjects = allProjects.filter(project => {
        const matchesSearch = project.name.toLowerCase().includes(searchTerm);
        const matchesStatus = !statusFilter || project.status.toLowerCase() === statusFilter;
        return matchesSearch && matchesStatus;
    });
    
    // Update global projects array for pagination
    allProjects = filteredProjects;
    currentPage = 1; // Reset to first page
    
    // Re-display projects and update pagination
    displayProjects();
    displayFavoriteProjects();
    updatePagination();
}

// Setup event listeners
function setupEventListeners() {
    // Add project form
    const addProjectForm = document.getElementById('addProjectForm');
    if (addProjectForm) {
        addProjectForm.addEventListener('submit', handleAddProject);
    }
    
    // Edit project form
    const editProjectForm = document.getElementById('editProjectForm');
    if (editProjectForm) {
        editProjectForm.addEventListener('submit', handleEditProject);
    }
    
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// Handle add project
function handleAddProject(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const logRetentionValue = formData.get('logRetentionValue');
    const logRetentionUnit = formData.get('logRetentionUnit');
    
    // Get selected project types
    const selectedTypes = [];
    const typeCheckboxes = e.target.querySelectorAll('input[name="projectType"]:checked');
    typeCheckboxes.forEach(checkbox => {
        selectedTypes.push(checkbox.value);
    });
    
    const projectData = {
        name: formData.get('projectName'),
        description: formData.get('projectDescription'),
        status: formData.get('projectStatus'),
        createdDate: new Date().toISOString().split('T')[0],
        teamMembers: formData.get('teamMembers'),
        projectType: selectedTypes.join(', '),
        logRetention: `${logRetentionValue} ${logRetentionUnit}`,
        totalUniqueApis: parseInt(formData.get('totalUniqueApis')) || 0,
        totalRequests: parseInt(formData.get('totalRequests')) || 0,
        totalUsers: parseInt(formData.get('totalUsers')) || 0,
        apiSuccessRate: parseFloat(formData.get('apiSuccessRate')) || 0
    };
    
    // Validate form
    if (!validateProjectForm(projectData)) {
        return;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Creating...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Mock project creation - replace with actual API call
        console.log('Creating project:', projectData);
        
        // Close modal and reload projects
        const modal = bootstrap.Modal.getInstance(document.getElementById('addProjectModal'));
        modal.hide();
        
        e.target.reset();
        loadProjects();
        
        // Show success message
        showAlert('Project created successfully!', 'success');
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 1500);
}

// Handle edit project
function handleEditProject(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const logRetentionValue = formData.get('logRetentionValue');
    const logRetentionUnit = formData.get('logRetentionUnit');
    
    // Get selected project types
    const selectedTypes = [];
    const typeCheckboxes = e.target.querySelectorAll('input[name="projectType"]:checked');
    typeCheckboxes.forEach(checkbox => {
        selectedTypes.push(checkbox.value);
    });
    
    const projectData = {
        id: formData.get('projectId'),
        name: formData.get('projectName'),
        description: formData.get('projectDescription'),
        status: formData.get('projectStatus'),
        teamMembers: formData.get('teamMembers'),
        projectType: selectedTypes.join(', '),
        logRetention: `${logRetentionValue} ${logRetentionUnit}`,
        totalUniqueApis: parseInt(formData.get('totalUniqueApis')) || 0,
        totalRequests: parseInt(formData.get('totalRequests')) || 0,
        totalUsers: parseInt(formData.get('totalUsers')) || 0,
        apiSuccessRate: parseFloat(formData.get('apiSuccessRate')) || 0
    };
    
    // Validate form
    if (!validateProjectForm(projectData)) {
        return;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Mock project update - replace with actual API call
        console.log('Updating project:', projectData);
        
        // Close modal and reload projects
        const modal = bootstrap.Modal.getInstance(document.getElementById('editProjectModal'));
        modal.hide();
        
        loadProjects();
        
        // Show success message
        showAlert('Project updated successfully!', 'success');
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 1500);
}

// Validate project form
function validateProjectForm(data) {
    if (!data.name) {
        showAlert('Project name is required!', 'danger');
        return false;
    }
    
    if (data.name.length < 3) {
        showAlert('Project name must be at least 3 characters long!', 'danger');
        return false;
    }
    
    if (data.apiSuccessRate < 0 || data.apiSuccessRate > 100) {
        showAlert('API Success Rate must be between 0 and 100!', 'danger');
        return false;
    }
    
    return true;
}

// Edit project
function editProject(projectId) {
    // Mock project data - replace with actual API call
    const project = {
        id: projectId,
        name: 'E-commerce Platform',
        description: 'Complete e-commerce solution with web and mobile apps',
        status: 'Active',
        teamMembers: 'John Doe, Jane Smith, Mike Johnson',
        projectType: 'Web, Mobile',
        logRetention: '30 days',
        totalUniqueApis: 25,
        totalRequests: 1250000,
        totalUsers: 50000,
        apiSuccessRate: 98.5
    };
    
    // Parse log retention
    const retentionMatch = project.logRetention.match(/(\d+)\s+(\w+)/);
    const retentionValue = retentionMatch ? retentionMatch[1] : '30';
    const retentionUnit = retentionMatch ? retentionMatch[2] : 'days';
    
    // Populate edit form
    document.getElementById('editProjectId').value = project.id;
    document.getElementById('editProjectName').value = project.name;
    document.getElementById('editProjectDescription').value = project.description;
    document.getElementById('editProjectStatus').value = project.status;
    document.getElementById('editTeamMembers').value = project.teamMembers;
    document.getElementById('editLogRetentionValue').value = retentionValue;
    document.getElementById('editLogRetentionUnit').value = retentionUnit;
    document.getElementById('editTotalUniqueApis').value = project.totalUniqueApis;
    document.getElementById('editTotalRequests').value = project.totalRequests;
    document.getElementById('editTotalUsers').value = project.totalUsers;
    document.getElementById('editApiSuccessRate').value = project.apiSuccessRate;
    
    // Populate project type checkboxes
    const projectTypes = project.projectType.split(', ').map(type => type.trim());
    document.getElementById('editTypeMobile').checked = projectTypes.includes('Mobile');
    document.getElementById('editTypeWeb').checked = projectTypes.includes('Web');
    document.getElementById('editTypeDesktop').checked = projectTypes.includes('Desktop');
    
    // Show edit modal
    const editModal = new bootstrap.Modal(document.getElementById('editProjectModal'));
    editModal.show();
}

// Delete project
function deleteProject(projectId) {
    if (confirm('Are you sure you want to delete this project? This action cannot be undone.')) {
        // Show loading state
        showAlert('Deleting project...', 'info');
        
        // Simulate API call
        setTimeout(() => {
            // Mock project deletion - replace with actual API call
            console.log('Deleting project:', projectId);
            
            loadProjects();
            showAlert('Project deleted successfully!', 'success');
        }, 1000);
    }
}

// Show alert message
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Logout function
function logout() {
    sessionStorage.removeItem('isAuthenticated');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('userEmail');
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    
    if (!localStorage.getItem('rememberMe')) {
        localStorage.removeItem('userEmail');
    }
    
    window.location.href = 'index.html';
} 