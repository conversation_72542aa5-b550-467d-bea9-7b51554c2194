// Invited Users page functionality
let allInvitedUsers = [];
let filteredUsers = [];
let currentPage = 1;
const usersPerPage = 10;

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuthentication();
    
    // Initialize invited users
    initializeInvitedUsers();
    
    // Setup event listeners
    setupEventListeners();
});

// Check if user is authenticated
function checkAuthentication() {
    const isAuthenticated = localStorage.getItem('isLoggedIn') === 'true' || sessionStorage.getItem('isAuthenticated') === 'true';
    if (!isAuthenticated) {
        window.location.href = 'index.html';
        return;
    }
    
    // Update user info in navigation
    const userData = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('userData') || '{}');
    const userDropdown = document.getElementById('userDropdown');
    if (userDropdown && userData.name) {
        userDropdown.innerHTML = `<i class="fas fa-user-circle me-1"></i>${userData.name}`;
    }
}

// Initialize invited users
function initializeInvitedUsers() {
    loadAllInvitedUsers();
    populateProjectFilter();
    updateStatistics();
    displayInvitedUsers();
}

// Load all invited users from all projects
function loadAllInvitedUsers() {
    allInvitedUsers = [];
    const projects = getStoredProjects();
    
    projects.forEach(project => {
        const projectUsers = JSON.parse(localStorage.getItem(`invitedUsers_${project.id}`) || '[]');
        projectUsers.forEach(user => {
            allInvitedUsers.push({
                ...user,
                projectId: project.id,
                projectName: project.name,
                invitedDate: user.invitedDate || new Date().toISOString().split('T')[0] // Default to today if not set
            });
        });
    });
    
    filteredUsers = [...allInvitedUsers];
}

// Get stored projects (mock data for now)
function getStoredProjects() {
    // This would typically come from localStorage or API
    // For now, we'll generate some mock projects and add sample data
    const projects = [
        { id: 1, name: 'E-commerce Platform' },
        { id: 2, name: 'Mobile Banking App' },
        { id: 3, name: 'Healthcare System' },
        { id: 4, name: 'Social Media Dashboard' },
        { id: 5, name: 'IoT Monitoring' }
    ];

    // Add sample invited users if none exist
    addSampleDataIfEmpty();

    return projects;
}

// Add sample data for demonstration
function addSampleDataIfEmpty() {
    const sampleUsers = [
        {
            name: 'Alice Johnson',
            email: '<EMAIL>',
            role: 'admin',
            invitedDate: '2024-01-15'
        },
        {
            name: 'Bob Smith',
            email: '<EMAIL>',
            role: 'user',
            invitedDate: '2024-01-20'
        },
        {
            name: 'Carol Davis',
            email: '<EMAIL>',
            role: 'super_admin',
            invitedDate: '2024-01-25'
        }
    ];

    // Add sample data to projects if no invited users exist
    for (let i = 1; i <= 3; i++) {
        const existingUsers = localStorage.getItem(`invitedUsers_${i}`);
        if (!existingUsers || JSON.parse(existingUsers).length === 0) {
            const projectUsers = sampleUsers.map((user, index) => ({
                ...user,
                name: `${user.name} (Project ${i})`,
                email: `${user.email.split('@')[0]}.p${i}@${user.email.split('@')[1]}`
            }));
            localStorage.setItem(`invitedUsers_${i}`, JSON.stringify(projectUsers));
        }
    }
}

// Populate project filter dropdown
function populateProjectFilter() {
    const filterProject = document.getElementById('filterProject');
    const projects = getStoredProjects();
    
    // Clear existing options except "All Projects"
    filterProject.innerHTML = '<option value="">All Projects</option>';
    
    projects.forEach(project => {
        const option = document.createElement('option');
        option.value = project.id;
        option.textContent = project.name;
        filterProject.appendChild(option);
    });
}

// Update statistics
function updateStatistics() {
    const totalUsers = allInvitedUsers.length;
    const activeProjects = new Set(allInvitedUsers.map(user => user.projectId)).size;
    const adminUsers = allInvitedUsers.filter(user => user.role === 'admin').length;
    const superAdminUsers = allInvitedUsers.filter(user => user.role === 'super_admin').length;
    
    document.getElementById('totalInvitedUsers').textContent = totalUsers;
    document.getElementById('activeProjects').textContent = activeProjects;
    document.getElementById('adminUsers').textContent = adminUsers;
    document.getElementById('superAdminUsers').textContent = superAdminUsers;
}

// Setup event listeners
function setupEventListeners() {
    // Search functionality
    const searchInput = document.getElementById('searchUsers');
    searchInput.addEventListener('input', debounce(applyFilters, 300));
    
    // Filter functionality
    const roleFilter = document.getElementById('filterRole');
    const projectFilter = document.getElementById('filterProject');
    
    roleFilter.addEventListener('change', applyFilters);
    projectFilter.addEventListener('change', applyFilters);
    
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
}

// Apply filters
function applyFilters() {
    const searchTerm = document.getElementById('searchUsers').value.toLowerCase();
    const roleFilter = document.getElementById('filterRole').value;
    const projectFilter = document.getElementById('filterProject').value;
    
    filteredUsers = allInvitedUsers.filter(user => {
        const matchesSearch = user.name.toLowerCase().includes(searchTerm) || 
                            user.email.toLowerCase().includes(searchTerm);
        const matchesRole = !roleFilter || user.role === roleFilter;
        const matchesProject = !projectFilter || user.projectId.toString() === projectFilter;
        
        return matchesSearch && matchesRole && matchesProject;
    });
    
    currentPage = 1;
    displayInvitedUsers();
}

// Clear all filters
function clearFilters() {
    document.getElementById('searchUsers').value = '';
    document.getElementById('filterRole').value = '';
    document.getElementById('filterProject').value = '';
    
    filteredUsers = [...allInvitedUsers];
    currentPage = 1;
    displayInvitedUsers();
}

// Display invited users
function displayInvitedUsers() {
    const tableBody = document.getElementById('invitedUsersTableBody');
    const emptyState = document.getElementById('emptyState');
    const paginationContainer = document.getElementById('paginationContainer');
    
    if (filteredUsers.length === 0) {
        tableBody.innerHTML = '';
        emptyState.style.display = 'block';
        paginationContainer.style.display = 'none';
        return;
    }
    
    emptyState.style.display = 'none';
    
    // Calculate pagination
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    const startIndex = (currentPage - 1) * usersPerPage;
    const endIndex = startIndex + usersPerPage;
    const currentUsers = filteredUsers.slice(startIndex, endIndex);
    
    // Populate table
    tableBody.innerHTML = '';
    currentUsers.forEach((user, index) => {
        const row = createUserRow(user, startIndex + index);
        tableBody.appendChild(row);
    });
    
    // Update pagination
    if (totalPages > 1) {
        updatePagination(totalPages);
        paginationContainer.style.display = 'block';
    } else {
        paginationContainer.style.display = 'none';
    }
}

// Create user row
function createUserRow(user, globalIndex) {
    const row = document.createElement('tr');
    const roleDisplayName = getRoleDisplayName(user.role || 'user');
    const roleClass = getRoleClass(user.role || 'user');
    
    row.innerHTML = `
        <td>
            <div class="d-flex align-items-center">
                <i class="fas fa-user-circle me-3 text-primary" style="font-size: 1.5rem;"></i>
                <div>
                    <div class="fw-bold">${user.name}</div>
                </div>
            </div>
        </td>
        <td>
            <a href="mailto:${user.email}" class="text-decoration-none">
                ${user.email}
            </a>
        </td>
        <td>
            <span class="badge role-badge ${roleClass}">${roleDisplayName}</span>
        </td>
        <td>
            <a href="project-details.html?id=${user.projectId}" class="text-decoration-none">
                ${user.projectName}
            </a>
        </td>
        <td>
            <small class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                ${formatDate(user.invitedDate)}
            </small>
        </td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-primary" onclick="viewUserDetails('${user.email}', ${user.projectId})" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="removeUser('${user.email}', ${user.projectId}, '${user.name}')" title="Remove User">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        </td>
    `;
    
    return row;
}

// Get role display name
function getRoleDisplayName(role) {
    const roleNames = {
        'user': 'User',
        'admin': 'Admin',
        'super_admin': 'Super Admin'
    };
    return roleNames[role] || 'User';
}

// Get role CSS class
function getRoleClass(role) {
    const roleClasses = {
        'user': 'role-user',
        'admin': 'role-admin',
        'super_admin': 'role-super-admin'
    };
    return roleClasses[role] || 'role-user';
}

// Format date
function formatDate(dateString) {
    const date = new Date(dateString);
    return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Update pagination
function updatePagination(totalPages) {
    const pagination = document.getElementById('pagination');
    pagination.innerHTML = '';
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage - 1})">Previous</a>`;
    pagination.appendChild(prevLi);
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changePage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changePage(${currentPage + 1})">Next</a>`;
    pagination.appendChild(nextLi);
}

// Change page
function changePage(page) {
    const totalPages = Math.ceil(filteredUsers.length / usersPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        displayInvitedUsers();
    }
}

// View user details
function viewUserDetails(email, projectId) {
    window.location.href = `project-details.html?id=${projectId}`;
}

// Remove user
function removeUser(email, projectId, userName) {
    if (confirm(`Are you sure you want to remove ${userName} (${email}) from the project?`)) {
        // Get current project users
        const projectUsers = JSON.parse(localStorage.getItem(`invitedUsers_${projectId}`) || '[]');
        
        // Remove user from project
        const updatedUsers = projectUsers.filter(user => user.email !== email);
        
        // Update localStorage
        localStorage.setItem(`invitedUsers_${projectId}`, JSON.stringify(updatedUsers));
        
        // Refresh the display
        loadAllInvitedUsers();
        updateStatistics();
        displayInvitedUsers();
        
        // Show success message
        showNotification(`${userName} has been removed from the project.`, 'success');
    }
}

// Export invited users
function exportInvitedUsers() {
    const csvContent = generateCSV(filteredUsers);
    downloadCSV(csvContent, 'invited-users.csv');
}

// Generate CSV content
function generateCSV(users) {
    const headers = ['Name', 'Email', 'Role', 'Project', 'Invited Date'];
    const csvRows = [headers.join(',')];
    
    users.forEach(user => {
        const row = [
            `"${user.name}"`,
            `"${user.email}"`,
            `"${getRoleDisplayName(user.role)}"`,
            `"${user.projectName}"`,
            `"${user.invitedDate}"`
        ];
        csvRows.push(row.join(','));
    });
    
    return csvRows.join('\n');
}

// Download CSV file
function downloadCSV(content, filename) {
    const blob = new Blob([content], { type: 'text/csv' });
    const url = window.URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = filename;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    window.URL.revokeObjectURL(url);
}

// Refresh invited users
function refreshInvitedUsers() {
    loadAllInvitedUsers();
    updateStatistics();
    displayInvitedUsers();
    showNotification('Invited users list refreshed successfully.', 'success');
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(notification);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}

// Logout function
function logout() {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    sessionStorage.clear();
    window.location.href = 'index.html';
}

// Debounce function for search
function debounce(func, wait) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            clearTimeout(timeout);
            func(...args);
        };
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
    };
}
