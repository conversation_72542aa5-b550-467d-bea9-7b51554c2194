// Logs page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuthentication();
    
    // Initialize logs
    initializeLogs();
    
    // Setup event listeners
    setupEventListeners();
});

// Check if user is authenticated
function checkAuthentication() {
    const isAuthenticated = localStorage.getItem('isLoggedIn') === 'true' || sessionStorage.getItem('isAuthenticated') === 'true';
    if (!isAuthenticated) {
        window.location.href = 'index.html';
        return;
    }
}

// Initialize logs
function initializeLogs() {
    loadLogs();
    setupFilters();
    updateLogStatistics();
}

// Load logs data
function loadLogs() {
    // Mock logs data - replace with actual API call
    const logs = generateMockLogs(50);
    displayLogs(logs);
}

// Generate mock logs
function generateMockLogs(count) {
    const logLevels = ['DEBUG', 'INFO', 'WARN', 'ERROR', 'FATAL'];
    const projects = ['E-commerce App', 'API Gateway', 'User Service'];
    const messages = [
        'Request processed successfully',
        'Database connection established',
        'Cache miss occurred',
        'User authentication successful',
        'API rate limit exceeded',
        'Memory usage high',
        'Network timeout detected',
        'File upload completed',
        'Email sent successfully',
        'Payment processed',
        'Database query executed',
        'External API call made',
        'User session created',
        'Password reset requested',
        'Log file rotated'
    ];
    
    const logs = [];
    for (let i = 0; i < count; i++) {
        const timestamp = new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000);
        logs.push({
            id: i + 1,
            timestamp: timestamp.toISOString(),
            project: projects[Math.floor(Math.random() * projects.length)],
            level: logLevels[Math.floor(Math.random() * logLevels.length)],
            message: messages[Math.floor(Math.random() * messages.length)],
            user: '<EMAIL>',
            ip: '192.168.1.' + Math.floor(Math.random() * 255),
            method: ['GET', 'POST', 'PUT', 'DELETE'][Math.floor(Math.random() * 4)],
            url: '/api/v1/' + ['users', 'products', 'orders', 'auth'][Math.floor(Math.random() * 4)],
            statusCode: [200, 201, 400, 401, 404, 500][Math.floor(Math.random() * 6)],
            responseTime: Math.floor(Math.random() * 1000) + 50
        });
    }
    
    return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

// Display logs in table
function displayLogs(logs) {
    const tableBody = document.getElementById('logsTableBody');
    tableBody.innerHTML = '';
    
    logs.forEach(log => {
        const row = createLogRow(log);
        tableBody.appendChild(row);
    });
}

// Create log table row
function createLogRow(log) {
    const row = document.createElement('tr');
    
    const levelClass = getLevelClass(log.level);
    const statusClass = getStatusClass(log.statusCode);
    
    row.innerHTML = `
        <td>${formatTimestamp(log.timestamp)}</td>
        <td>${log.project}</td>
        <td><span class="badge ${levelClass}">${log.level}</span></td>
        <td>${log.message}</td>
        <td>${log.user}</td>
        <td>${log.ip}</td>
        <td>
            <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetails(${log.id})">
                <i class="fas fa-eye"></i>
            </button>
        </td>
    `;
    
    return row;
}

// Get level class for badge
function getLevelClass(level) {
    const classes = {
        'DEBUG': 'bg-secondary',
        'INFO': 'bg-info',
        'WARN': 'bg-warning',
        'ERROR': 'bg-danger',
        'FATAL': 'bg-dark'
    };
    return classes[level] || 'bg-secondary';
}

// Get status class for badge
function getStatusClass(statusCode) {
    if (statusCode >= 200 && statusCode < 300) return 'bg-success';
    if (statusCode >= 400 && statusCode < 500) return 'bg-warning';
    if (statusCode >= 500) return 'bg-danger';
    return 'bg-secondary';
}

// Format timestamp
function formatTimestamp(timestamp) {
    const date = new Date(timestamp);
    const now = new Date();
    const diff = now - date;
    
    if (diff < 60000) return 'Just now';
    if (diff < 3600000) return Math.floor(diff / 60000) + ' min ago';
    if (diff < 86400000) return Math.floor(diff / 3600000) + ' hours ago';
    return date.toLocaleDateString() + ' ' + date.toLocaleTimeString();
}

// Setup filters
function setupFilters() {
    const searchBtn = document.getElementById('searchBtn');
    const clearFiltersBtn = document.getElementById('clearFiltersBtn');
    
    if (searchBtn) {
        searchBtn.addEventListener('click', applyFilters);
    }
    
    if (clearFiltersBtn) {
        clearFiltersBtn.addEventListener('click', clearFilters);
    }
    
    // Add enter key support for search
    const searchInput = document.getElementById('searchLogs');
    if (searchInput) {
        searchInput.addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                applyFilters();
            }
        });
    }
}

// Apply filters
function applyFilters() {
    const projectFilter = document.getElementById('projectFilter').value;
    const levelFilter = document.getElementById('levelFilter').value;
    const dateFrom = document.getElementById('dateFrom').value;
    const dateTo = document.getElementById('dateTo').value;
    const searchTerm = document.getElementById('searchLogs').value.toLowerCase();
    
    // Show loading state
    showLoadingState();
    
    // Simulate API call with filters
    setTimeout(() => {
        const filteredLogs = generateMockLogs(25); // Reduced for demo
        displayLogs(filteredLogs);
        updateLogStatistics();
        hideLoadingState();
    }, 500);
}

// Clear filters
function clearFilters() {
    document.getElementById('projectFilter').value = '';
    document.getElementById('levelFilter').value = '';
    document.getElementById('dateFrom').value = '';
    document.getElementById('dateTo').value = '';
    document.getElementById('searchLogs').value = '';
    
    loadLogs();
}

// Update log statistics
function updateLogStatistics() {
    // Mock statistics - replace with actual API call
    const stats = {
        total: Math.floor(Math.random() * 1000) + 2000,
        info: Math.floor(Math.random() * 500) + 1000,
        warning: Math.floor(Math.random() * 200) + 400,
        error: Math.floor(Math.random() * 100) + 150,
        debug: Math.floor(Math.random() * 300) + 800,
        fatal: Math.floor(Math.random() * 10)
    };
    
    document.getElementById('totalLogs').textContent = stats.total.toLocaleString();
    document.getElementById('infoLogs').textContent = stats.info.toLocaleString();
    document.getElementById('warningLogs').textContent = stats.warning.toLocaleString();
    document.getElementById('errorLogs').textContent = stats.error.toLocaleString();
    document.getElementById('debugLogs').textContent = stats.debug.toLocaleString();
    document.getElementById('fatalLogs').textContent = stats.fatal.toLocaleString();
}

// Show loading state
function showLoadingState() {
    const tableBody = document.getElementById('logsTableBody');
    tableBody.innerHTML = `
        <tr>
            <td colspan="7" class="text-center py-4">
                <div class="spinner mx-auto"></div>
                <p class="mt-2 text-muted">Loading logs...</p>
            </td>
        </tr>
    `;
}

// Hide loading state
function hideLoadingState() {
    // Loading state will be replaced when logs are loaded
}

// Setup event listeners
function setupEventListeners() {
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
    
    // Refresh button
    const refreshBtn = document.getElementById('refreshBtn');
    if (refreshBtn) {
        refreshBtn.addEventListener('click', function() {
            loadLogs();
            updateLogStatistics();
        });
    }
    
    // Export button
    const exportBtn = document.getElementById('exportBtn');
    if (exportBtn) {
        exportBtn.addEventListener('click', function() {
            exportLogs();
        });
    }
}

// View log details
function viewLogDetails(logId) {
    // Mock log details - replace with actual API call
    const logDetails = {
        timestamp: new Date().toISOString(),
        project: 'E-commerce App',
        level: 'ERROR',
        user: '<EMAIL>',
        ip: '*************',
        method: 'POST',
        url: '/api/v1/orders',
        statusCode: 500,
        responseTime: 2450,
        message: 'Database connection failed: Connection timeout after 30 seconds',
        requestHeaders: JSON.stringify({
            'Content-Type': 'application/json',
            'Authorization': 'Bearer eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...',
            'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36'
        }, null, 2),
        responseHeaders: JSON.stringify({
            'Content-Type': 'application/json',
            'X-Request-ID': 'req-12345',
            'Server': 'nginx/1.18.0'
        }, null, 2),
        requestBody: JSON.stringify({
            'orderId': 'ORD-12345',
            'items': [
                {'productId': 1, 'quantity': 2},
                {'productId': 3, 'quantity': 1}
            ]
        }, null, 2),
        responseBody: JSON.stringify({
            'error': 'Internal Server Error',
            'message': 'Database connection failed',
            'timestamp': new Date().toISOString()
        }, null, 2),
        stackTrace: `Error: Database connection failed
    at Database.connect (/app/database.js:45:12)
    at OrderService.createOrder (/app/services/order.js:23:8)
    at OrderController.create (/app/controllers/order.js:15:4)
    at /app/routes/order.js:8:12`
    };
    
    // Populate modal
    document.getElementById('detailTimestamp').textContent = formatTimestamp(logDetails.timestamp);
    document.getElementById('detailProject').textContent = logDetails.project;
    document.getElementById('detailLevel').textContent = logDetails.level;
    document.getElementById('detailUser').textContent = logDetails.user;
    document.getElementById('detailIP').textContent = logDetails.ip;
    document.getElementById('detailMethod').textContent = logDetails.method;
    document.getElementById('detailURL').textContent = logDetails.url;
    document.getElementById('detailStatusCode').textContent = logDetails.statusCode;
    document.getElementById('detailResponseTime').textContent = logDetails.responseTime + 'ms';
    document.getElementById('detailMessage').textContent = logDetails.message;
    document.getElementById('detailRequestHeaders').textContent = logDetails.requestHeaders;
    document.getElementById('detailResponseHeaders').textContent = logDetails.responseHeaders;
    document.getElementById('detailRequestBody').textContent = logDetails.requestBody;
    document.getElementById('detailResponseBody').textContent = logDetails.responseBody;
    document.getElementById('detailStackTrace').textContent = logDetails.stackTrace;
    
    // Show stack trace section for errors
    const stackTraceSection = document.getElementById('stackTraceSection');
    if (logDetails.level === 'ERROR' || logDetails.level === 'FATAL') {
        stackTraceSection.style.display = 'block';
    } else {
        stackTraceSection.style.display = 'none';
    }
    
    // Show modal
    const modal = new bootstrap.Modal(document.getElementById('logDetailModal'));
    modal.show();
}

// Export logs
function exportLogs() {
    // Show loading state
    const exportBtn = document.getElementById('exportBtn');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-1"></i>Exporting...';
    exportBtn.disabled = true;
    
    // Simulate export process
    setTimeout(() => {
        // Create and download CSV file
        const csvContent = generateCSV();
        const blob = new Blob([csvContent], { type: 'text/csv' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'logs-export-' + new Date().toISOString().split('T')[0] + '.csv';
        a.click();
        window.URL.revokeObjectURL(url);
        
        // Reset button
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
        
        // Show success message
        showAlert('Logs exported successfully!', 'success');
    }, 2000);
}

// Generate CSV content
function generateCSV() {
    const headers = ['Timestamp', 'Project', 'Level', 'Message', 'User', 'IP Address', 'Method', 'URL', 'Status Code', 'Response Time'];
    const logs = generateMockLogs(100);
    
    const csvRows = [headers.join(',')];
    
    logs.forEach(log => {
        const row = [
            log.timestamp,
            log.project,
            log.level,
            log.message,
            log.user,
            log.ip,
            log.method,
            log.url,
            log.statusCode,
            log.responseTime
        ].map(field => `"${field}"`).join(',');
        csvRows.push(row);
    });
    
    return csvRows.join('\n');
}

// Show alert message
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Logout function
function logout() {
    // Clear session data
    sessionStorage.removeItem('isAuthenticated');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('userEmail');
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    
    // Clear local storage if not remembering
    if (!localStorage.getItem('rememberMe')) {
        localStorage.removeItem('userEmail');
    }
    
    // Redirect to login
    window.location.href = 'index.html';
} 