// Project Details JavaScript
let currentProject = null;
let currentLogs = [];
let currentPage = 1;
let logsPerPage = 10;

// Invite Users logic
let invitedUsers = [];

// API Keys logic
let apiKeys = [];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Setup logout functionality
    document.getElementById('logoutBtn').addEventListener('click', logout);
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('id');
    if (projectId) {
        const stored = localStorage.getItem('invitedUsers_' + projectId);
        if (stored) {
            invitedUsers = JSON.parse(stored);
            // Migrate existing users without roles (backward compatibility)
            let needsUpdate = false;
            invitedUsers = invitedUsers.map(user => {
                if (!user.role) {
                    needsUpdate = true;
                    return { ...user, role: 'user' }; // Default to 'user' role
                }
                return user;
            });

            // Update localStorage if migration was needed
            if (needsUpdate) {
                localStorage.setItem('invitedUsers_' + projectId, JSON.stringify(invitedUsers));
            }
        }
        renderInvitedUsers();

        // Load API keys for this project
        const storedApiKeys = localStorage.getItem('apiKeys_' + projectId);
        if (storedApiKeys) {
            apiKeys = JSON.parse(storedApiKeys);
        } else {
            // Add sample API keys for demonstration
            addSampleApiKeys(projectId);
        }
        // Always ensure we have sample data for demonstration
        if (apiKeys.length === 0) {
            addSampleApiKeys(projectId);
        }
        renderApiKeys();

        // Add sample invited users if none exist
        if (invitedUsers.length === 0) {
            addSampleInvitedUsers(projectId);
        }
        renderInvitedUsersTab();
    }
    // Invite form event
    const inviteForm = document.getElementById('inviteUserForm');
    if (inviteForm) {
        inviteForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const name = document.getElementById('inviteName').value.trim();
            const email = document.getElementById('inviteEmail').value.trim();
            const role = document.getElementById('inviteRole').value;

            if (!name || !validateEmail(email) || !role) {
                alert('Please enter a valid name, email, and select a role.');
                return;
            }

            // Check if user is already invited
            if (invitedUsers.some(user => user.email.toLowerCase() === email.toLowerCase())) {
                alert('This user has already been invited to the project.');
                return;
            }

            invitedUsers.push({ name, email, role });
            localStorage.setItem('invitedUsers_' + projectId, JSON.stringify(invitedUsers));
            renderInvitedUsers();
            renderInvitedUsersTab();
            inviteForm.reset();
            showNotification(`${name} has been invited to the project as ${getRoleDisplayName(role)}.`, 'success');
        });
    }

    // Invite user form submission (new tab form)
    const tabForm = document.getElementById('inviteUserFormTab');
    if (tabForm) {
        tabForm.addEventListener('submit', function(e) {
            e.preventDefault();

            const name = document.getElementById('inviteNameTab').value.trim();
            const email = document.getElementById('inviteEmailTab').value.trim();
            const role = document.getElementById('inviteRoleTab').value;

            if (!name || !validateEmail(email) || !role) {
                alert('Please enter a valid name, email, and select a role.');
                return;
            }

            // Check if user is already invited
            if (invitedUsers.some(user => user.email.toLowerCase() === email.toLowerCase())) {
                alert('This user has already been invited to the project.');
                return;
            }

            const newUser = {
                id: Date.now(),
                name: name,
                email: email,
                role: role,
                invitedDate: new Date().toISOString(),
                status: 'pending'
            };

            invitedUsers.push(newUser);
            localStorage.setItem('invitedUsers_' + projectId, JSON.stringify(invitedUsers));
            renderInvitedUsers();
            renderInvitedUsersTab();
            tabForm.reset();
            showNotification(`${name} has been invited to the project as ${getRoleDisplayName(role)}.`, 'success');
        });
    }
});

// Logout function
function logout() {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    sessionStorage.clear();
    window.location.href = 'index.html';
}

// Load project details
function loadProjectDetails(projectId) {
    // Simulate API call to get project details
    const projects = JSON.parse(localStorage.getItem('projects') || '[]');
    currentProject = projects.find(p => p.id === projectId);
    
    if (!currentProject) {
        // Project not found, redirect to projects page
        window.location.href = 'projects.html';
        return;
    }

    // Update page title and breadcrumb
    document.getElementById('projectTitle').textContent = currentProject.name;
    document.getElementById('projectName').textContent = currentProject.name;
    
    // Load overview data
    loadOverviewData();
    
    // Load logs data
    loadLogsData();
    
    // Load reports data
    loadReportsData();
}

// Load overview section data
function loadOverviewData() {
    if (!currentProject) return;

    document.getElementById('overviewProjectId').textContent = currentProject.id;
    document.getElementById('overviewProjectName').textContent = currentProject.name;
    document.getElementById('overviewDescription').textContent = currentProject.description || 'No description available';
    document.getElementById('overviewStatus').textContent = currentProject.status;
    document.getElementById('overviewType').textContent = currentProject.projectType ? currentProject.projectType.join(', ') : 'N/A';
    document.getElementById('overviewTeam').textContent = currentProject.teamMembers || 'Not assigned';
    document.getElementById('overviewRetention').textContent = currentProject.logRetention || '30 days';
    document.getElementById('overviewLogEnabled').textContent = currentProject.logEnabled ? 'Log Enabled' : 'Log Disabled';
    document.getElementById('overviewLogEnabled').className = 'badge ms-2 ' + (currentProject.logEnabled ? 'bg-success' : 'bg-secondary');
    
    // Update metrics
    document.getElementById('overviewApis').textContent = currentProject.totalApis || '25';
    document.getElementById('overviewRequests').textContent = currentProject.totalRequests || '1.2M';
    document.getElementById('overviewUsers').textContent = currentProject.totalUsers || '50K';
    document.getElementById('overviewSuccess').textContent = currentProject.apiSuccessRate || '98.5%';
    

}

// Load logs data
function loadLogsData() {
    if (!currentProject) return;

    // Simulate API call to get logs
    const mockLogs = generateMockLogs();
    currentLogs = mockLogs;
    
    // Update logs count
    document.getElementById('logsCount').textContent = currentLogs.length;
    
    // Display logs
    displayLogs();
}

// Generate mock logs data
function generateMockLogs() {
    const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG'];
    const messages = [
        'Database connection established successfully',
        'User authentication failed',
        'API request processed',
        'Memory usage high',
        'Cache miss occurred',
        'External service timeout',
        'Data validation passed',
        'File upload completed',
        'Email sent successfully',
        'Backup process started'
    ];
    
    const sources = ['API Gateway', 'User Service', 'Database', 'Cache Service', 'Email Service'];
    
    const logs = [];
    for (let i = 0; i < 50; i++) {
        logs.push({
            id: i + 1,
            timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
            level: levels[Math.floor(Math.random() * levels.length)],
            message: messages[Math.floor(Math.random() * messages.length)],
            source: sources[Math.floor(Math.random() * sources.length)]
        });
    }
    
    return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

// Display logs in table
function displayLogs() {
    const tableBody = document.getElementById('logsTableBody');
    const startIndex = (currentPage - 1) * logsPerPage;
    const endIndex = startIndex + logsPerPage;
    const pageLogs = currentLogs.slice(startIndex, endIndex);
    
    tableBody.innerHTML = '';
    
    pageLogs.forEach(log => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatTimestamp(log.timestamp)}</td>
            <td><span class="badge bg-${getLevelColor(log.level)}">${log.level}</span></td>
            <td>${log.message}</td>
            <td>${log.source}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetails(${log.id})">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
    
    // Update pagination
    updateLogsPagination();
}

// Get color for log level
function getLevelColor(level) {
    switch (level) {
        case 'ERROR': return 'danger';
        case 'WARN': return 'warning';
        case 'INFO': return 'info';
        case 'DEBUG': return 'secondary';
        default: return 'secondary';
    }
}

// Format timestamp
function formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString();
}

// Update logs pagination
function updateLogsPagination() {
    const totalPages = Math.ceil(currentLogs.length / logsPerPage);
    const pagination = document.getElementById('logsPagination');
    
    pagination.innerHTML = '';
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changeLogsPage(${currentPage - 1})">Previous</a>`;
    pagination.appendChild(prevLi);
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changeLogsPage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changeLogsPage(${currentPage + 1})">Next</a>`;
    pagination.appendChild(nextLi);
}

// Change logs page
function changeLogsPage(page) {
    const totalPages = Math.ceil(currentLogs.length / logsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        displayLogs();
    }
}

// Filter logs
function filterLogs() {
    const levelFilter = document.getElementById('logLevelFilter').value;
    const dateFilter = document.getElementById('logDateFilter').value;
    const searchTerm = document.getElementById('logSearch').value.toLowerCase();
    
    let filteredLogs = currentLogs;
    
    // Apply level filter
    if (levelFilter) {
        filteredLogs = filteredLogs.filter(log => log.level === levelFilter);
    }
    
    // Apply date filter
    if (dateFilter) {
        const now = new Date();
        let cutoffDate;
        
        switch (dateFilter) {
            case '1h':
                cutoffDate = new Date(now.getTime() - 60 * 60 * 1000);
                break;
            case '24h':
                cutoffDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
        }
        
        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= cutoffDate);
    }
    
    // Apply search filter
    if (searchTerm) {
        filteredLogs = filteredLogs.filter(log => 
            log.message.toLowerCase().includes(searchTerm) ||
            log.source.toLowerCase().includes(searchTerm)
        );
    }
    
    // Update display
    currentLogs = filteredLogs;
    currentPage = 1;
    displayLogs();
}

// Export logs
function exportLogs() {
    // Simulate export functionality
    alert('Logs export functionality would be implemented here');
}

// Refresh logs
function refreshLogs() {
    loadLogsData();
}

// View log details
function viewLogDetails(logId) {
    const log = currentLogs.find(l => l.id === logId);
    if (log) {
        alert(`Log Details:\nID: ${log.id}\nTimestamp: ${formatTimestamp(log.timestamp)}\nLevel: ${log.level}\nMessage: ${log.message}\nSource: ${log.source}`);
    }
}

// Load reports data
function loadReportsData() {
    if (!currentProject) return;
    
    // Initialize charts after a short delay to ensure DOM is ready
    setTimeout(() => {
        initializeCharts();
    }, 100);
}

// Initialize charts
function initializeCharts() {
    // Request Volume Chart
    const requestCtx = document.getElementById('requestChart');
    if (requestCtx) {
        new Chart(requestCtx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Requests',
                    data: [1200, 1900, 3000, 5000, 2000, 3000, 4000],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // Error Distribution Chart
    const errorCtx = document.getElementById('errorChart');
    if (errorCtx) {
        new Chart(errorCtx, {
            type: 'doughnut',
            data: {
                labels: ['Error', 'Warning', 'Info', 'Debug'],
                datasets: [{
                    data: [12, 19, 3, 5],
                    backgroundColor: [
                        'rgb(255, 99, 132)',
                        'rgb(255, 205, 86)',
                        'rgb(54, 162, 235)',
                        'rgb(201, 203, 207)'
                    ]
                }]
            },
            options: {
                responsive: true
            }
        });
    }
    
    // Performance Trends Chart
    const performanceCtx = document.getElementById('performanceChart');
    if (performanceCtx) {
        new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Response Time (ms)',
                    data: [200, 250, 180, 300, 220, 280, 240],
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Generate report
function generateReport() {
    const reportType = document.getElementById('reportType').value;
    const reportPeriod = document.getElementById('reportPeriod').value;
    const reportFormat = document.getElementById('reportFormat').value;
    
    // Simulate report generation
    alert(`Generating ${reportType} report for ${reportPeriod} in ${reportFormat} format...`);
}

// Export report
function exportReport() {
    // Simulate export functionality
    alert('Report export functionality would be implemented here');
}

// Go back to projects page
function goBack() {
    window.location.href = 'projects.html';
}

// Edit project
function editProject() {
    if (currentProject) {
        // Redirect to projects page with edit mode
        window.location.href = `projects.html?edit=${currentProject.id}`;
    }
}

function renderInvitedUsers() {
    const list = document.getElementById('invitedUsersList');
    const countBadge = document.getElementById('invitedUsersCount');

    list.innerHTML = '';

    // Update count badge
    if (countBadge) {
        countBadge.textContent = invitedUsers.length;
    }

    if (invitedUsers.length === 0) {
        list.innerHTML = '<li class="list-group-item text-muted">No users invited yet.</li>';
        return;
    }
    invitedUsers.forEach((user, index) => {
        const li = document.createElement('li');
        li.className = 'list-group-item d-flex justify-content-between align-items-center invited-user-item';

        // Create user info div
        const userInfo = document.createElement('div');
        const roleDisplayName = getRoleDisplayName(user.role || 'user');
        const roleClass = getRoleClass(user.role || 'user');

        userInfo.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-user-circle me-3 text-primary" style="font-size: 1.5rem;"></i>
                <div class="invited-user-info">
                    <div class="user-name">${user.name}</div>
                    <div class="user-email">${user.email}</div>
                    <div class="mt-2">
                        <span class="badge role-badge ${roleClass}">${roleDisplayName}</span>
                    </div>
                </div>
            </div>
        `;

        // Create remove button
        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn btn-outline-danger btn-sm';
        removeBtn.innerHTML = '<i class="fas fa-trash-alt me-1"></i>Remove';
        removeBtn.title = 'Remove user from project';
        removeBtn.onclick = () => removeInvitedUser(index);

        li.appendChild(userInfo);
        li.appendChild(removeBtn);
        list.appendChild(li);
    });
}

// Remove invited user
function removeInvitedUser(index) {
    if (index < 0 || index >= invitedUsers.length) {
        return;
    }

    const user = invitedUsers[index];

    // Show confirmation dialog
    const roleDisplayName = getRoleDisplayName(user.role || 'user');
    if (confirm(`Are you sure you want to remove ${user.name} (${user.email}) with role "${roleDisplayName}" from this project?`)) {
        // Remove user from array
        invitedUsers.splice(index, 1);

        // Update localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const projectId = urlParams.get('id');
        if (projectId) {
            localStorage.setItem('invitedUsers_' + projectId, JSON.stringify(invitedUsers));
        }

        // Re-render the list
        renderInvitedUsers();

        // Show success message
        showNotification(`${user.name} has been removed from the project.`, 'success');
    }
}

// Show notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to body
    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

// Helper function to get role display name
function getRoleDisplayName(role) {
    const roleMap = {
        'user': '👤 User',
        'admin': '⚙️ Admin',
        'super_admin': '👑 Super Admin'
    };
    return roleMap[role] || '👤 User';
}

// Helper function to get role CSS class
function getRoleClass(role) {
    const roleClassMap = {
        'user': 'bg-secondary',
        'admin': 'bg-warning text-dark',
        'super_admin': 'bg-danger'
    };
    return roleClassMap[role] || 'bg-secondary';
}

function validateEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}

// ===== INVITED USERS TAB MANAGEMENT =====

// Add sample invited users for demonstration
function addSampleInvitedUsers(projectId) {
    const sampleUsers = [
        {
            id: 'sample_user_1',
            name: 'Alice Johnson',
            email: '<EMAIL>',
            role: 'admin',
            invitedDate: new Date(Date.now() - 15 * 24 * 60 * 60 * 1000).toISOString(), // 15 days ago
            status: 'accepted',
            lastActivity: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString() // 2 days ago
        },
        {
            id: 'sample_user_2',
            name: 'Bob Smith',
            email: '<EMAIL>',
            role: 'user',
            invitedDate: new Date(Date.now() - 10 * 24 * 60 * 60 * 1000).toISOString(), // 10 days ago
            status: 'accepted',
            lastActivity: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString() // 1 day ago
        },
        {
            id: 'sample_user_3',
            name: 'Carol Davis',
            email: '<EMAIL>',
            role: 'super_admin',
            invitedDate: new Date(Date.now() - 5 * 24 * 60 * 60 * 1000).toISOString(), // 5 days ago
            status: 'pending',
            lastActivity: null
        },
        {
            id: 'sample_user_4',
            name: 'David Wilson',
            email: '<EMAIL>',
            role: 'user',
            invitedDate: new Date(Date.now() - 20 * 24 * 60 * 60 * 1000).toISOString(), // 20 days ago
            status: 'expired',
            lastActivity: null
        }
    ];

    invitedUsers = sampleUsers;
    localStorage.setItem('invitedUsers_' + projectId, JSON.stringify(invitedUsers));
}

// Render invited users in the tab
function renderInvitedUsersTab() {
    const tableBody = document.getElementById('invitedUsersTableBody');
    const emptyState = document.getElementById('invitedUsersEmptyState');
    const tabCountBadge = document.getElementById('invitedUsersTabCount');

    // Update statistics
    updateInvitedUsersStats();

    // Update tab count
    if (tabCountBadge) tabCountBadge.textContent = invitedUsers.length;

    if (invitedUsers.length === 0) {
        if (tableBody) tableBody.innerHTML = '';
        if (emptyState) emptyState.style.display = 'block';
        return;
    }

    if (emptyState) emptyState.style.display = 'none';
    if (tableBody) {
        tableBody.innerHTML = '';
        invitedUsers.forEach((user, index) => {
            const row = createInvitedUserRow(user, index);
            tableBody.appendChild(row);
        });
    }
}

// Update invited users statistics
function updateInvitedUsersStats() {
    const totalElement = document.getElementById('totalInvitedUsers');
    const acceptedElement = document.getElementById('acceptedInvitations');
    const pendingElement = document.getElementById('pendingInvitations');
    const expiredElement = document.getElementById('expiredInvitations');

    const total = invitedUsers.length;
    const accepted = invitedUsers.filter(user => user.status === 'accepted').length;
    const pending = invitedUsers.filter(user => user.status === 'pending').length;
    const expired = invitedUsers.filter(user => user.status === 'expired').length;

    if (totalElement) totalElement.textContent = total;
    if (acceptedElement) acceptedElement.textContent = accepted;
    if (pendingElement) pendingElement.textContent = pending;
    if (expiredElement) expiredElement.textContent = expired;
}

// Create invited user table row
function createInvitedUserRow(user, index) {
    const row = document.createElement('tr');
    const statusClass = getStatusClass(user.status);
    const statusText = getStatusText(user.status);

    row.innerHTML = `
        <td>
            <div>
                <strong>${user.name}</strong>
                <br><small class="text-muted">${user.email}</small>
            </div>
        </td>
        <td>
            <span class="badge ${getRoleClass(user.role)}">${getRoleDisplayName(user.role)}</span>
        </td>
        <td>
            <small class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                ${new Date(user.invitedDate).toLocaleDateString()}
            </small>
        </td>
        <td>
            <span class="badge ${statusClass}">${statusText}</span>
        </td>
        <td>
            <small class="text-muted">
                ${user.lastActivity ? new Date(user.lastActivity).toLocaleDateString() : 'Never'}
            </small>
        </td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-primary" onclick="resendInvitation('${user.id}')" title="Resend Invitation">
                    <i class="fas fa-paper-plane"></i>
                </button>
                <button class="btn btn-sm btn-outline-info" onclick="viewUserDetails('${user.id}')" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="removeInvitedUserFromTab('${user.id}')" title="Remove User">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        </td>
    `;

    return row;
}

// Helper functions for user status
function getStatusClass(status) {
    const statusMap = {
        'accepted': 'bg-success',
        'pending': 'bg-warning text-dark',
        'expired': 'bg-danger'
    };
    return statusMap[status] || 'bg-secondary';
}

function getStatusText(status) {
    const statusMap = {
        'accepted': 'Accepted',
        'pending': 'Pending',
        'expired': 'Expired'
    };
    return statusMap[status] || 'Unknown';
}

// User management functions
function resendInvitation(userId) {
    const user = invitedUsers.find(u => u.id === userId);
    if (user) {
        showNotification(`Invitation resent to ${user.name} (${user.email})`, 'success');
    }
}

function viewUserDetails(userId) {
    const user = invitedUsers.find(u => u.id === userId);
    if (!user) return;

    const details = `
        <strong>Name:</strong> ${user.name}<br>
        <strong>Email:</strong> ${user.email}<br>
        <strong>Role:</strong> ${getRoleDisplayName(user.role)}<br>
        <strong>Invited:</strong> ${new Date(user.invitedDate).toLocaleString()}<br>
        <strong>Status:</strong> ${getStatusText(user.status)}<br>
        <strong>Last Activity:</strong> ${user.lastActivity ? new Date(user.lastActivity).toLocaleString() : 'Never'}
    `;

    showNotification(details, 'info');
}

function removeInvitedUserFromTab(userId) {
    const user = invitedUsers.find(u => u.id === userId);
    if (!user) return;

    if (!confirm(`Are you sure you want to remove ${user.name} from this project?`)) {
        return;
    }

    const index = invitedUsers.findIndex(u => u.id === userId);
    if (index > -1) {
        invitedUsers.splice(index, 1);
    }

    // Save to localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('id');
    if (projectId) {
        localStorage.setItem('invitedUsers_' + projectId, JSON.stringify(invitedUsers));
    }

    // Refresh displays
    renderInvitedUsers();
    renderInvitedUsersTab();
    showNotification(`${user.name} has been removed from the project.`, 'success');
}

// Export and bulk invite functions
function exportInvitedUsers() {
    if (invitedUsers.length === 0) {
        alert('No invited users to export.');
        return;
    }

    const csvContent = "data:text/csv;charset=utf-8,"
        + "Name,Email,Role,Invited Date,Status,Last Activity\n"
        + invitedUsers.map(user =>
            `"${user.name}","${user.email}","${user.role}","${user.invitedDate}","${user.status}","${user.lastActivity || 'Never'}"`
        ).join("\n");

    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", "invited_users.csv");
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    showNotification('Invited users exported successfully!', 'success');
}

function bulkInviteUsers() {
    alert('Bulk invite feature coming soon! You can upload a CSV file to invite multiple users at once.');
}

// ===== API KEYS MANAGEMENT =====

// Add sample API keys for demonstration
function addSampleApiKeys(projectId) {
    const sampleKeys = [
        {
            id: 'sample_1',
            name: 'Production API Key',
            description: 'Main API key for production environment',
            key: 'lms_prod_k8x9m2n4p6q8r1s3t5u7v9w2x4y6z8a1b3c5d7e9f2g4h6j8k1m3n5p7q9r2s4t6u8v1w3x5y7z9',
            permissions: { read: true, write: true, delete: false },
            created: new Date(Date.now() - 30 * 24 * 60 * 60 * 1000).toISOString(), // 30 days ago
            lastUsed: new Date(Date.now() - 2 * 24 * 60 * 60 * 1000).toISOString(), // 2 days ago
            expiry: null,
            status: 'active'
        },
        {
            id: 'sample_2',
            name: 'Development API Key',
            description: 'API key for development and testing',
            key: 'lms_dev_a1b2c3d4e5f6g7h8i9j0k1l2m3n4o5p6q7r8s9t0u1v2w3x4y5z6a7b8c9d0e1f2g3h4i5j6k7l8m9n0',
            permissions: { read: true, write: true, delete: true },
            created: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days ago
            lastUsed: new Date(Date.now() - 1 * 24 * 60 * 60 * 1000).toISOString(), // 1 day ago
            expiry: new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(), // 90 days from now
            status: 'active'
        },
        {
            id: 'sample_3',
            name: 'Mobile App API Key',
            description: 'API key for mobile application integration',
            key: 'lms_mobile_z9y8x7w6v5u4t3s2r1q0p9o8n7m6l5k4j3i2h1g0f9e8d7c6b5a4z3y2x1w0v9u8t7s6r5q4p3o2n1m0',
            permissions: { read: true, write: true, delete: false },
            created: new Date(Date.now() - 14 * 24 * 60 * 60 * 1000).toISOString(), // 14 days ago
            lastUsed: new Date(Date.now() - 3 * 60 * 60 * 1000).toISOString(), // 3 hours ago
            expiry: new Date(Date.now() + 180 * 24 * 60 * 60 * 1000).toISOString(), // 180 days from now
            status: 'active'
        }
    ];

    apiKeys = sampleKeys;
    localStorage.setItem('apiKeys_' + projectId, JSON.stringify(apiKeys));
}

// Generate new API key
function generateNewApiKey() {
    const modal = new bootstrap.Modal(document.getElementById('apiKeyModal'));
    modal.show();
}

// Create API key
function createApiKey() {
    const name = document.getElementById('apiKeyName').value.trim();
    const description = document.getElementById('apiKeyDescription').value.trim();
    const expiry = document.getElementById('apiKeyExpiry').value;
    const permissions = {
        read: document.getElementById('permissionRead').checked,
        write: document.getElementById('permissionWrite').checked,
        delete: document.getElementById('permissionDelete').checked
    };

    if (!name) {
        alert('Please enter a name for the API key.');
        return;
    }

    // Generate API key
    const apiKey = generateApiKeyString();
    const now = new Date();
    let expiryDate = null;

    if (expiry !== 'never') {
        expiryDate = new Date();
        expiryDate.setDate(expiryDate.getDate() + parseInt(expiry));
    }

    const newApiKey = {
        id: Date.now().toString(),
        name: name,
        description: description,
        key: apiKey,
        permissions: permissions,
        created: now.toISOString(),
        lastUsed: null,
        expiry: expiryDate ? expiryDate.toISOString() : null,
        status: 'active'
    };

    // Add to array and save
    apiKeys.push(newApiKey);
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('id');
    if (projectId) {
        localStorage.setItem('apiKeys_' + projectId, JSON.stringify(apiKeys));
    }

    // Close modal and show success
    const modal = bootstrap.Modal.getInstance(document.getElementById('apiKeyModal'));
    modal.hide();

    // Clear form
    document.getElementById('apiKeyForm').reset();

    // Show the generated key
    showGeneratedApiKey(newApiKey);

    // Refresh display
    renderApiKeys();
}

// Generate API key string
function generateApiKeyString() {
    const prefix = 'lms_'; // Log Management System prefix
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789';
    let result = prefix;
    for (let i = 0; i < 32; i++) {
        result += chars.charAt(Math.floor(Math.random() * chars.length));
    }
    return result;
}

// Show generated API key modal
function showGeneratedApiKey(apiKeyData) {
    document.getElementById('generatedApiKey').value = apiKeyData.key;
    document.getElementById('displayKeyName').textContent = apiKeyData.name;
    document.getElementById('displayKeyCreated').textContent = new Date(apiKeyData.created).toLocaleString();

    // Update usage example
    const example = `curl -X POST https://api.yourproject.com/logs \\
  -H "Authorization: Bearer ${apiKeyData.key}" \\
  -H "Content-Type: application/json" \\
  -d '{"level": "info", "message": "Hello World"}'`;
    document.getElementById('apiUsageExample').textContent = example;

    const modal = new bootstrap.Modal(document.getElementById('apiKeyDisplayModal'));
    modal.show();
}

// Copy API key to clipboard
function copyApiKey() {
    const apiKeyInput = document.getElementById('generatedApiKey');
    apiKeyInput.select();
    apiKeyInput.setSelectionRange(0, 99999); // For mobile devices

    try {
        document.execCommand('copy');
        showNotification('API key copied to clipboard!', 'success');
    } catch (err) {
        // Fallback for modern browsers
        navigator.clipboard.writeText(apiKeyInput.value).then(() => {
            showNotification('API key copied to clipboard!', 'success');
        }).catch(() => {
            showNotification('Failed to copy API key. Please copy manually.', 'error');
        });
    }
}

// Copy full API key from table
function copyFullApiKey(apiKey) {
    try {
        navigator.clipboard.writeText(apiKey).then(() => {
            showNotification('API key copied to clipboard!', 'success');
        }).catch(() => {
            // Fallback
            const textArea = document.createElement('textarea');
            textArea.value = apiKey;
            document.body.appendChild(textArea);
            textArea.select();
            document.execCommand('copy');
            document.body.removeChild(textArea);
            showNotification('API key copied to clipboard!', 'success');
        });
    } catch (err) {
        showNotification('Failed to copy API key. Please copy manually.', 'error');
    }
}

// Render API keys table
function renderApiKeys() {
    const tableBody = document.getElementById('apiKeysTableBody');
    const emptyState = document.getElementById('apiKeysEmptyState');
    const countBadge = document.getElementById('apiKeysCount');
    const totalKeysDisplay = document.getElementById('totalApiKeys');

    // Update counts
    const activeKeys = apiKeys.filter(key => key.status === 'active').length;
    if (countBadge) countBadge.textContent = activeKeys;
    if (totalKeysDisplay) totalKeysDisplay.textContent = activeKeys;

    if (apiKeys.length === 0) {
        if (tableBody) tableBody.innerHTML = '';
        if (emptyState) emptyState.style.display = 'block';
        return;
    }

    if (emptyState) emptyState.style.display = 'none';
    if (tableBody) {
        tableBody.innerHTML = '';
        apiKeys.forEach((apiKey, index) => {
            const row = createApiKeyRow(apiKey, index);
            tableBody.appendChild(row);
        });
    }
}

// Create API key table row
function createApiKeyRow(apiKey, index) {
    const row = document.createElement('tr');
    const isExpired = apiKey.expiry && new Date(apiKey.expiry) < new Date();
    const statusClass = isExpired ? 'danger' : (apiKey.status === 'active' ? 'success' : 'secondary');
    const statusText = isExpired ? 'Expired' : (apiKey.status === 'active' ? 'Active' : 'Inactive');

    // Mask the API key for security
    const maskedKey = apiKey.key.substring(0, 8) + '...' + apiKey.key.substring(apiKey.key.length - 4);

    row.innerHTML = `
        <td>
            <div>
                <strong>${apiKey.name}</strong>
                ${apiKey.description ? `<br><small class="text-muted">${apiKey.description}</small>` : ''}
            </div>
        </td>
        <td>
            <code class="text-muted">${maskedKey}</code>
            <button class="btn btn-sm btn-outline-secondary ms-2" onclick="copyFullApiKey('${apiKey.key}')" title="Copy full key">
                <i class="fas fa-copy"></i>
            </button>
        </td>
        <td>
            <small class="text-muted">
                <i class="fas fa-calendar me-1"></i>
                ${new Date(apiKey.created).toLocaleDateString()}
            </small>
        </td>
        <td>
            <small class="text-muted">
                ${apiKey.lastUsed ? new Date(apiKey.lastUsed).toLocaleDateString() : 'Never'}
            </small>
        </td>
        <td>
            <span class="badge bg-${statusClass}">${statusText}</span>
            ${apiKey.expiry ? `<br><small class="text-muted">Expires: ${new Date(apiKey.expiry).toLocaleDateString()}</small>` : ''}
        </td>
        <td>
            <div class="btn-group" role="group">
                <button class="btn btn-sm btn-outline-info" onclick="viewApiKeyDetails('${apiKey.id}')" title="View Details">
                    <i class="fas fa-eye"></i>
                </button>
                <button class="btn btn-sm btn-outline-warning" onclick="regenerateApiKey('${apiKey.id}')" title="Regenerate">
                    <i class="fas fa-sync-alt"></i>
                </button>
                <button class="btn btn-sm btn-outline-danger" onclick="deleteApiKey('${apiKey.id}')" title="Delete">
                    <i class="fas fa-trash-alt"></i>
                </button>
            </div>
        </td>
    `;

    return row;
}

// View API key details
function viewApiKeyDetails(apiKeyId) {
    const apiKey = apiKeys.find(key => key.id === apiKeyId);
    if (!apiKey) return;

    const permissions = [];
    if (apiKey.permissions.read) permissions.push('Read');
    if (apiKey.permissions.write) permissions.push('Write');
    if (apiKey.permissions.delete) permissions.push('Delete');

    const details = `
        <strong>Name:</strong> ${apiKey.name}<br>
        <strong>Description:</strong> ${apiKey.description || 'No description'}<br>
        <strong>Created:</strong> ${new Date(apiKey.created).toLocaleString()}<br>
        <strong>Last Used:</strong> ${apiKey.lastUsed ? new Date(apiKey.lastUsed).toLocaleString() : 'Never'}<br>
        <strong>Expires:</strong> ${apiKey.expiry ? new Date(apiKey.expiry).toLocaleString() : 'Never'}<br>
        <strong>Permissions:</strong> ${permissions.join(', ')}<br>
        <strong>Status:</strong> ${apiKey.status}
    `;

    showNotification(details, 'info');
}

// Regenerate API key
function regenerateApiKey(apiKeyId) {
    if (!confirm('Are you sure you want to regenerate this API key? The old key will stop working immediately.')) {
        return;
    }

    const apiKeyIndex = apiKeys.findIndex(key => key.id === apiKeyId);
    if (apiKeyIndex === -1) return;

    // Generate new key
    const newKey = generateApiKeyString();
    apiKeys[apiKeyIndex].key = newKey;
    apiKeys[apiKeyIndex].created = new Date().toISOString();
    apiKeys[apiKeyIndex].lastUsed = null;

    // Save to localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('id');
    if (projectId) {
        localStorage.setItem('apiKeys_' + projectId, JSON.stringify(apiKeys));
    }

    // Show the new key
    showGeneratedApiKey(apiKeys[apiKeyIndex]);

    // Refresh display
    renderApiKeys();
    showNotification('API key regenerated successfully!', 'success');
}

// Delete API key
function deleteApiKey(apiKeyId) {
    const apiKey = apiKeys.find(key => key.id === apiKeyId);
    if (!apiKey) return;

    if (!confirm(`Are you sure you want to delete the API key "${apiKey.name}"? This action cannot be undone.`)) {
        return;
    }

    // Remove from array
    const index = apiKeys.findIndex(key => key.id === apiKeyId);
    if (index > -1) {
        apiKeys.splice(index, 1);
    }

    // Save to localStorage
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('id');
    if (projectId) {
        localStorage.setItem('apiKeys_' + projectId, JSON.stringify(apiKeys));
    }

    // Refresh display
    renderApiKeys();
    showNotification('API key deleted successfully!', 'success');
}

// Show notification
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    document.body.appendChild(notification);

    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.parentNode.removeChild(notification);
        }
    }, 5000);
}