// Project Details JavaScript
let currentProject = null;
let currentLogs = [];
let currentPage = 1;
let logsPerPage = 10;

// Invite Users logic
let invitedUsers = [];

// Initialize the page
document.addEventListener('DOMContentLoaded', function() {
    // Setup logout functionality
    document.getElementById('logoutBtn').addEventListener('click', logout);
    const urlParams = new URLSearchParams(window.location.search);
    const projectId = urlParams.get('id');
    if (projectId) {
        const stored = localStorage.getItem('invitedUsers_' + projectId);
        if (stored) invitedUsers = JSON.parse(stored);
        renderInvitedUsers();
    }
    // Invite form event
    const inviteForm = document.getElementById('inviteUserForm');
    if (inviteForm) {
        inviteForm.addEventListener('submit', function(e) {
            e.preventDefault();
            const name = document.getElementById('inviteName').value.trim();
            const email = document.getElementById('inviteEmail').value.trim();
            if (!name || !validateEmail(email)) {
                alert('Please enter a valid name and email.');
                return;
            }

            // Check if user is already invited
            if (invitedUsers.some(user => user.email.toLowerCase() === email.toLowerCase())) {
                alert('This user has already been invited to the project.');
                return;
            }
            invitedUsers.push({ name, email });
            localStorage.setItem('invitedUsers_' + projectId, JSON.stringify(invitedUsers));
            renderInvitedUsers();
            inviteForm.reset();
            showNotification(`${name} has been invited to the project.`, 'success');
        });
    }
});

// Logout function
function logout() {
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    sessionStorage.clear();
    window.location.href = 'index.html';
}

// Load project details
function loadProjectDetails(projectId) {
    // Simulate API call to get project details
    const projects = JSON.parse(localStorage.getItem('projects') || '[]');
    currentProject = projects.find(p => p.id === projectId);
    
    if (!currentProject) {
        // Project not found, redirect to projects page
        window.location.href = 'projects.html';
        return;
    }

    // Update page title and breadcrumb
    document.getElementById('projectTitle').textContent = currentProject.name;
    document.getElementById('projectName').textContent = currentProject.name;
    
    // Load overview data
    loadOverviewData();
    
    // Load logs data
    loadLogsData();
    
    // Load reports data
    loadReportsData();
}

// Load overview section data
function loadOverviewData() {
    if (!currentProject) return;

    document.getElementById('overviewProjectId').textContent = currentProject.id;
    document.getElementById('overviewProjectName').textContent = currentProject.name;
    document.getElementById('overviewDescription').textContent = currentProject.description || 'No description available';
    document.getElementById('overviewStatus').textContent = currentProject.status;
    document.getElementById('overviewType').textContent = currentProject.projectType ? currentProject.projectType.join(', ') : 'N/A';
    document.getElementById('overviewTeam').textContent = currentProject.teamMembers || 'Not assigned';
    document.getElementById('overviewRetention').textContent = currentProject.logRetention || '30 days';
    document.getElementById('overviewLogEnabled').textContent = currentProject.logEnabled ? 'Log Enabled' : 'Log Disabled';
    document.getElementById('overviewLogEnabled').className = 'badge ms-2 ' + (currentProject.logEnabled ? 'bg-success' : 'bg-secondary');
    
    // Update metrics
    document.getElementById('overviewApis').textContent = currentProject.totalApis || '25';
    document.getElementById('overviewRequests').textContent = currentProject.totalRequests || '1.2M';
    document.getElementById('overviewUsers').textContent = currentProject.totalUsers || '50K';
    document.getElementById('overviewSuccess').textContent = currentProject.apiSuccessRate || '98.5%';
    
    // Update progress bar
    const successRate = parseFloat(currentProject.apiSuccessRate || '98.5');
    document.getElementById('overviewProgress').style.width = successRate + '%';
}

// Load logs data
function loadLogsData() {
    if (!currentProject) return;

    // Simulate API call to get logs
    const mockLogs = generateMockLogs();
    currentLogs = mockLogs;
    
    // Update logs count
    document.getElementById('logsCount').textContent = currentLogs.length;
    
    // Display logs
    displayLogs();
}

// Generate mock logs data
function generateMockLogs() {
    const levels = ['ERROR', 'WARN', 'INFO', 'DEBUG'];
    const messages = [
        'Database connection established successfully',
        'User authentication failed',
        'API request processed',
        'Memory usage high',
        'Cache miss occurred',
        'External service timeout',
        'Data validation passed',
        'File upload completed',
        'Email sent successfully',
        'Backup process started'
    ];
    
    const sources = ['API Gateway', 'User Service', 'Database', 'Cache Service', 'Email Service'];
    
    const logs = [];
    for (let i = 0; i < 50; i++) {
        logs.push({
            id: i + 1,
            timestamp: new Date(Date.now() - Math.random() * 7 * 24 * 60 * 60 * 1000).toISOString(),
            level: levels[Math.floor(Math.random() * levels.length)],
            message: messages[Math.floor(Math.random() * messages.length)],
            source: sources[Math.floor(Math.random() * sources.length)]
        });
    }
    
    return logs.sort((a, b) => new Date(b.timestamp) - new Date(a.timestamp));
}

// Display logs in table
function displayLogs() {
    const tableBody = document.getElementById('logsTableBody');
    const startIndex = (currentPage - 1) * logsPerPage;
    const endIndex = startIndex + logsPerPage;
    const pageLogs = currentLogs.slice(startIndex, endIndex);
    
    tableBody.innerHTML = '';
    
    pageLogs.forEach(log => {
        const row = document.createElement('tr');
        row.innerHTML = `
            <td>${formatTimestamp(log.timestamp)}</td>
            <td><span class="badge bg-${getLevelColor(log.level)}">${log.level}</span></td>
            <td>${log.message}</td>
            <td>${log.source}</td>
            <td>
                <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetails(${log.id})">
                    <i class="fas fa-eye"></i>
                </button>
            </td>
        `;
        tableBody.appendChild(row);
    });
    
    // Update pagination
    updateLogsPagination();
}

// Get color for log level
function getLevelColor(level) {
    switch (level) {
        case 'ERROR': return 'danger';
        case 'WARN': return 'warning';
        case 'INFO': return 'info';
        case 'DEBUG': return 'secondary';
        default: return 'secondary';
    }
}

// Format timestamp
function formatTimestamp(timestamp) {
    return new Date(timestamp).toLocaleString();
}

// Update logs pagination
function updateLogsPagination() {
    const totalPages = Math.ceil(currentLogs.length / logsPerPage);
    const pagination = document.getElementById('logsPagination');
    
    pagination.innerHTML = '';
    
    // Previous button
    const prevLi = document.createElement('li');
    prevLi.className = `page-item ${currentPage === 1 ? 'disabled' : ''}`;
    prevLi.innerHTML = `<a class="page-link" href="#" onclick="changeLogsPage(${currentPage - 1})">Previous</a>`;
    pagination.appendChild(prevLi);
    
    // Page numbers
    for (let i = 1; i <= totalPages; i++) {
        const li = document.createElement('li');
        li.className = `page-item ${i === currentPage ? 'active' : ''}`;
        li.innerHTML = `<a class="page-link" href="#" onclick="changeLogsPage(${i})">${i}</a>`;
        pagination.appendChild(li);
    }
    
    // Next button
    const nextLi = document.createElement('li');
    nextLi.className = `page-item ${currentPage === totalPages ? 'disabled' : ''}`;
    nextLi.innerHTML = `<a class="page-link" href="#" onclick="changeLogsPage(${currentPage + 1})">Next</a>`;
    pagination.appendChild(nextLi);
}

// Change logs page
function changeLogsPage(page) {
    const totalPages = Math.ceil(currentLogs.length / logsPerPage);
    if (page >= 1 && page <= totalPages) {
        currentPage = page;
        displayLogs();
    }
}

// Filter logs
function filterLogs() {
    const levelFilter = document.getElementById('logLevelFilter').value;
    const dateFilter = document.getElementById('logDateFilter').value;
    const searchTerm = document.getElementById('logSearch').value.toLowerCase();
    
    let filteredLogs = currentLogs;
    
    // Apply level filter
    if (levelFilter) {
        filteredLogs = filteredLogs.filter(log => log.level === levelFilter);
    }
    
    // Apply date filter
    if (dateFilter) {
        const now = new Date();
        let cutoffDate;
        
        switch (dateFilter) {
            case '1h':
                cutoffDate = new Date(now.getTime() - 60 * 60 * 1000);
                break;
            case '24h':
                cutoffDate = new Date(now.getTime() - 24 * 60 * 60 * 1000);
                break;
            case '7d':
                cutoffDate = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case '30d':
                cutoffDate = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
                break;
        }
        
        filteredLogs = filteredLogs.filter(log => new Date(log.timestamp) >= cutoffDate);
    }
    
    // Apply search filter
    if (searchTerm) {
        filteredLogs = filteredLogs.filter(log => 
            log.message.toLowerCase().includes(searchTerm) ||
            log.source.toLowerCase().includes(searchTerm)
        );
    }
    
    // Update display
    currentLogs = filteredLogs;
    currentPage = 1;
    displayLogs();
}

// Export logs
function exportLogs() {
    // Simulate export functionality
    alert('Logs export functionality would be implemented here');
}

// Refresh logs
function refreshLogs() {
    loadLogsData();
}

// View log details
function viewLogDetails(logId) {
    const log = currentLogs.find(l => l.id === logId);
    if (log) {
        alert(`Log Details:\nID: ${log.id}\nTimestamp: ${formatTimestamp(log.timestamp)}\nLevel: ${log.level}\nMessage: ${log.message}\nSource: ${log.source}`);
    }
}

// Load reports data
function loadReportsData() {
    if (!currentProject) return;
    
    // Initialize charts after a short delay to ensure DOM is ready
    setTimeout(() => {
        initializeCharts();
    }, 100);
}

// Initialize charts
function initializeCharts() {
    // Request Volume Chart
    const requestCtx = document.getElementById('requestChart');
    if (requestCtx) {
        new Chart(requestCtx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Requests',
                    data: [1200, 1900, 3000, 5000, 2000, 3000, 4000],
                    borderColor: 'rgb(75, 192, 192)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
    
    // Error Distribution Chart
    const errorCtx = document.getElementById('errorChart');
    if (errorCtx) {
        new Chart(errorCtx, {
            type: 'doughnut',
            data: {
                labels: ['Error', 'Warning', 'Info', 'Debug'],
                datasets: [{
                    data: [12, 19, 3, 5],
                    backgroundColor: [
                        'rgb(255, 99, 132)',
                        'rgb(255, 205, 86)',
                        'rgb(54, 162, 235)',
                        'rgb(201, 203, 207)'
                    ]
                }]
            },
            options: {
                responsive: true
            }
        });
    }
    
    // Performance Trends Chart
    const performanceCtx = document.getElementById('performanceChart');
    if (performanceCtx) {
        new Chart(performanceCtx, {
            type: 'line',
            data: {
                labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri', 'Sat', 'Sun'],
                datasets: [{
                    label: 'Response Time (ms)',
                    data: [200, 250, 180, 300, 220, 280, 240],
                    borderColor: 'rgb(255, 99, 132)',
                    tension: 0.1
                }]
            },
            options: {
                responsive: true,
                scales: {
                    y: {
                        beginAtZero: true
                    }
                }
            }
        });
    }
}

// Generate report
function generateReport() {
    const reportType = document.getElementById('reportType').value;
    const reportPeriod = document.getElementById('reportPeriod').value;
    const reportFormat = document.getElementById('reportFormat').value;
    
    // Simulate report generation
    alert(`Generating ${reportType} report for ${reportPeriod} in ${reportFormat} format...`);
}

// Export report
function exportReport() {
    // Simulate export functionality
    alert('Report export functionality would be implemented here');
}

// Go back to projects page
function goBack() {
    window.location.href = 'projects.html';
}

// Edit project
function editProject() {
    if (currentProject) {
        // Redirect to projects page with edit mode
        window.location.href = `projects.html?edit=${currentProject.id}`;
    }
}

function renderInvitedUsers() {
    const list = document.getElementById('invitedUsersList');
    const countBadge = document.getElementById('invitedUsersCount');

    list.innerHTML = '';

    // Update count badge
    if (countBadge) {
        countBadge.textContent = invitedUsers.length;
    }

    if (invitedUsers.length === 0) {
        list.innerHTML = '<li class="list-group-item text-muted">No users invited yet.</li>';
        return;
    }
    invitedUsers.forEach((user, index) => {
        const li = document.createElement('li');
        li.className = 'list-group-item d-flex justify-content-between align-items-center';

        // Create user info div
        const userInfo = document.createElement('div');
        userInfo.innerHTML = `
            <div class="d-flex align-items-center">
                <i class="fas fa-user-circle me-2 text-primary"></i>
                <div>
                    <div class="fw-semibold">${user.name}</div>
                    <small class="text-muted">${user.email}</small>
                </div>
            </div>
        `;

        // Create remove button
        const removeBtn = document.createElement('button');
        removeBtn.className = 'btn btn-outline-danger btn-sm';
        removeBtn.innerHTML = '<i class="fas fa-trash-alt me-1"></i>Remove';
        removeBtn.title = 'Remove user from project';
        removeBtn.onclick = () => removeInvitedUser(index);

        li.appendChild(userInfo);
        li.appendChild(removeBtn);
        list.appendChild(li);
    });
}

// Remove invited user
function removeInvitedUser(index) {
    if (index < 0 || index >= invitedUsers.length) {
        return;
    }

    const user = invitedUsers[index];

    // Show confirmation dialog
    if (confirm(`Are you sure you want to remove ${user.name} (${user.email}) from this project?`)) {
        // Remove user from array
        invitedUsers.splice(index, 1);

        // Update localStorage
        const urlParams = new URLSearchParams(window.location.search);
        const projectId = urlParams.get('id');
        if (projectId) {
            localStorage.setItem('invitedUsers_' + projectId, JSON.stringify(invitedUsers));
        }

        // Re-render the list
        renderInvitedUsers();

        // Show success message
        showNotification(`${user.name} has been removed from the project.`, 'success');
    }
}

// Show notification function
function showNotification(message, type = 'info') {
    // Create notification element
    const notification = document.createElement('div');
    notification.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    notification.style.cssText = 'top: 20px; right: 20px; z-index: 1050; min-width: 300px;';
    notification.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;

    // Add to body
    document.body.appendChild(notification);

    // Auto remove after 3 seconds
    setTimeout(() => {
        if (notification.parentNode) {
            notification.remove();
        }
    }, 3000);
}

function validateEmail(email) {
    return /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email);
}