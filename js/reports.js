// Reports page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuthentication();
    
    // Initialize reports
    initializeReports();
    
    // Setup event listeners
    setupEventListeners();
});

// Check if user is authenticated
function checkAuthentication() {
    const isAuthenticated = localStorage.getItem('isLoggedIn') === 'true' || sessionStorage.getItem('isAuthenticated') === 'true';
    if (!isAuthenticated) {
        window.location.href = 'index.html';
        return;
    }
}

// Initialize reports
function initializeReports() {
    initializeCharts();
    loadReportData();
    setupReportFilters();
}

// Initialize charts
function initializeCharts() {
    createLogVolumeChart();
    createLogLevelChart();
    createErrorSourcesChart();
    createResponseTimeChart();
}

// Create log volume chart
function createLogVolumeChart() {
    const ctx = document.getElementById('logVolumeChart');
    if (!ctx) return;
    
    const labels = [];
    const data = [];
    
    // Generate last 7 days data
    for (let i = 6; i >= 0; i--) {
        const date = new Date();
        date.setDate(date.getDate() - i);
        labels.push(date.toLocaleDateString('en-US', { month: 'short', day: 'numeric' }));
        data.push(Math.floor(Math.random() * 1000) + 500);
    }
    
    new Chart(ctx, {
        type: 'line',
        data: {
            labels: labels,
            datasets: [{
                label: 'Log Volume',
                data: data,
                borderColor: '#667eea',
                backgroundColor: 'rgba(102, 126, 234, 0.1)',
                borderWidth: 3,
                fill: true,
                tension: 0.4
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// Create log level chart
function createLogLevelChart() {
    const ctx = document.getElementById('logLevelChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'doughnut',
        data: {
            labels: ['Info', 'Warning', 'Error', 'Debug', 'Fatal'],
            datasets: [{
                data: [45, 25, 15, 12, 3],
                backgroundColor: [
                    '#17a2b8',
                    '#ffc107',
                    '#dc3545',
                    '#6c757d',
                    '#721c24'
                ],
                borderWidth: 0
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    position: 'bottom',
                    labels: {
                        padding: 20,
                        usePointStyle: true
                    }
                }
            }
        }
    });
}

// Create error sources chart
function createErrorSourcesChart() {
    const ctx = document.getElementById('errorSourcesChart');
    if (!ctx) return;
    
    new Chart(ctx, {
        type: 'bar',
        data: {
            labels: ['Database', 'API Gateway', 'User Service', 'Payment Service', 'Email Service'],
            datasets: [{
                label: 'Errors',
                data: [23, 18, 12, 8, 5],
                backgroundColor: '#dc3545',
                borderRadius: 5
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    grid: {
                        color: 'rgba(0, 0, 0, 0.1)'
                    }
                },
                x: {
                    grid: {
                        display: false
                    }
                }
            }
        }
    });
}

// Create response time chart
function createResponseTimeChart() {
    const ctx = document.getElementById('responseTimeChart');
    if (!ctx) return;
    
    const data = [];
    for (let i = 0; i < 100; i++) {
        data.push(Math.floor(Math.random() * 2000) + 50);
    }
    
    new Chart(ctx, {
        type: 'histogram',
        data: {
            datasets: [{
                label: 'Response Time (ms)',
                data: data,
                backgroundColor: 'rgba(102, 126, 234, 0.6)',
                borderColor: '#667eea',
                borderWidth: 1
            }]
        },
        options: {
            responsive: true,
            maintainAspectRatio: false,
            plugins: {
                legend: {
                    display: false
                }
            },
            scales: {
                y: {
                    beginAtZero: true,
                    title: {
                        display: true,
                        text: 'Frequency'
                    }
                },
                x: {
                    title: {
                        display: true,
                        text: 'Response Time (ms)'
                    }
                }
            }
        }
    });
}

// Load report data
function loadReportData() {
    // Mock report data - replace with actual API call
    const reportData = [
        {
            date: '2024-01-15',
            project: 'E-commerce App',
            totalRequests: 15420,
            successRate: '98.5%',
            avgResponseTime: '245ms',
            errorCount: 231,
            peakHour: '14:00'
        },
        {
            date: '2024-01-14',
            project: 'API Gateway',
            totalRequests: 12850,
            successRate: '99.2%',
            avgResponseTime: '189ms',
            errorCount: 103,
            peakHour: '15:30'
        },
        {
            date: '2024-01-13',
            project: 'User Service',
            totalRequests: 8920,
            successRate: '97.8%',
            avgResponseTime: '312ms',
            errorCount: 198,
            peakHour: '13:15'
        }
    ];
    
    displayReportTable(reportData);
}

// Display report table
function displayReportTable(data) {
    const tableBody = document.getElementById('reportTableBody');
    if (!tableBody) return;
    
    tableBody.innerHTML = '';
    
    data.forEach(row => {
        const tr = document.createElement('tr');
        tr.innerHTML = `
            <td>${formatDate(row.date)}</td>
            <td>${row.project}</td>
            <td>${row.totalRequests.toLocaleString()}</td>
            <td><span class="badge bg-success">${row.successRate}</span></td>
            <td>${row.avgResponseTime}</td>
            <td>${row.errorCount}</td>
            <td>${row.peakHour}</td>
        `;
        tableBody.appendChild(tr);
    });
}

// Format date
function formatDate(dateString) {
    return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
    });
}

// Setup report filters
function setupReportFilters() {
    const generateReportBtn = document.getElementById('generateReportBtn');
    const exportReportBtn = document.getElementById('exportReportBtn');
    const scheduleReportBtn = document.getElementById('scheduleReportBtn');
    
    if (generateReportBtn) {
        generateReportBtn.addEventListener('click', generateReport);
    }
    
    if (exportReportBtn) {
        exportReportBtn.addEventListener('click', exportReport);
    }
    
    if (scheduleReportBtn) {
        scheduleReportBtn.addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('scheduleReportModal'));
            modal.show();
        });
    }
}

// Generate report
function generateReport() {
    const project = document.getElementById('reportProject').value;
    const period = document.getElementById('reportPeriod').value;
    const type = document.getElementById('reportType').value;
    
    // Show loading state
    const generateBtn = document.getElementById('generateReportBtn');
    const originalText = generateBtn.innerHTML;
    generateBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Generating...';
    generateBtn.disabled = true;
    
    // Simulate report generation
    setTimeout(() => {
        // Update charts with new data
        updateCharts();
        
        // Update statistics
        updateStatistics();
        
        // Show success message
        showAlert('Report generated successfully!', 'success');
        
        generateBtn.innerHTML = originalText;
        generateBtn.disabled = false;
    }, 2000);
}

// Update charts with new data
function updateCharts() {
    // This would update the charts with new data based on filters
    console.log('Updating charts with new data...');
}

// Update statistics
function updateStatistics() {
    // Mock updated statistics
    const stats = {
        totalRequests: Math.floor(Math.random() * 50000) + 30000,
        successRate: (Math.random() * 5 + 95).toFixed(1) + '%',
        avgResponseTime: Math.floor(Math.random() * 300 + 150) + 'ms',
        errorCount: Math.floor(Math.random() * 500 + 100)
    };
    
    document.getElementById('totalRequests').textContent = stats.totalRequests.toLocaleString();
    document.getElementById('successRate').textContent = stats.successRate;
    document.getElementById('avgResponseTime').textContent = stats.avgResponseTime;
    document.getElementById('errorCount').textContent = stats.errorCount.toLocaleString();
}

// Export report
function exportReport() {
    const exportBtn = document.getElementById('exportReportBtn');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Exporting...';
    exportBtn.disabled = true;
    
    setTimeout(() => {
        // Create and download report
        const reportContent = generateReportContent();
        const blob = new Blob([reportContent], { type: 'text/html' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'log-report-' + new Date().toISOString().split('T')[0] + '.html';
        a.click();
        window.URL.revokeObjectURL(url);
        
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
        
        showAlert('Report exported successfully!', 'success');
    }, 1500);
}

// Generate report content
function generateReportContent() {
    return `
        <!DOCTYPE html>
        <html>
        <head>
            <title>Log Management Report</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                table { border-collapse: collapse; width: 100%; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: left; }
                th { background-color: #f2f2f2; }
                .header { text-align: center; margin-bottom: 30px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>Log Management System Report</h1>
                <p>Generated on: ${new Date().toLocaleString()}</p>
            </div>
            <h2>Summary Statistics</h2>
            <table>
                <tr><th>Metric</th><th>Value</th></tr>
                <tr><td>Total Requests</td><td>45,678</td></tr>
                <tr><td>Success Rate</td><td>98.5%</td></tr>
                <tr><td>Average Response Time</td><td>245ms</td></tr>
                <tr><td>Total Errors</td><td>684</td></tr>
            </table>
        </body>
        </html>
    `;
}

// Setup event listeners
function setupEventListeners() {
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
    
    // Schedule report form
    const scheduleReportForm = document.getElementById('scheduleReportForm');
    if (scheduleReportForm) {
        scheduleReportForm.addEventListener('submit', function(e) {
            e.preventDefault();
            scheduleReport();
        });
    }
}

// Schedule report
function scheduleReport() {
    const formData = new FormData(document.getElementById('scheduleReportForm'));
    const reportData = {
        name: formData.get('reportName'),
        frequency: formData.get('reportFrequency'),
        email: formData.get('reportEmail'),
        format: formData.get('reportFormat')
    };
    
    // Show loading state
    const submitBtn = document.getElementById('scheduleReportForm').querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Scheduling...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('scheduleReportModal'));
        modal.hide();
        
        // Reset form
        document.getElementById('scheduleReportForm').reset();
        
        // Show success message
        showAlert('Report scheduled successfully!', 'success');
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 1500);
}

// Show alert message
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Logout function
function logout() {
    // Clear session data
    sessionStorage.removeItem('isAuthenticated');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('userEmail');
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    
    // Clear local storage if not remembering
    if (!localStorage.getItem('rememberMe')) {
        localStorage.removeItem('userEmail');
    }
    
    // Redirect to login
    window.location.href = 'index.html';
} 