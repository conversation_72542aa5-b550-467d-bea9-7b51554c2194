// Dashboard functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuthentication();
    
    // Initialize dashboard
    initializeDashboard();
    
    // Setup event listeners
    setupEventListeners();
});

// Check if user is authenticated
function checkAuthentication() {
    const isAuthenticated = localStorage.getItem('isLoggedIn') === 'true' || sessionStorage.getItem('isAuthenticated') === 'true';
    if (!isAuthenticated) {
        window.location.href = 'index.html';
        return;
    }
    
    // Update user info in navigation
    const userData = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('userData') || '{}');
    const userDropdown = document.getElementById('userDropdown');
    if (userDropdown && userData.name) {
        userDropdown.innerHTML = `<i class="fas fa-user-circle me-1"></i>${userData.name}`;
    }
}

// Initialize dashboard
function initializeDashboard() {
    // Update real-time stats
    updateStats();
    
    // Start real-time updates
    setInterval(updateStats, 30000); // Update every 30 seconds
    
    // Add fade-in animation to cards
    const cards = document.querySelectorAll('.card');
    cards.forEach((card, index) => {
        card.style.opacity = '0';
        card.style.transform = 'translateY(20px)';
        
        setTimeout(() => {
            card.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            card.style.opacity = '1';
            card.style.transform = 'translateY(0)';
        }, index * 100);
    });
}

// Update dashboard statistics
function updateStats() {
    // Simulate real-time data updates
    const stats = {
        totalProjects: Math.floor(Math.random() * 5) + 10,
        activeLogs: Math.floor(Math.random() * 500) + 2500,
        errorsToday: Math.floor(Math.random() * 10) + 15,
        systemHealth: Math.floor(Math.random() * 10) + 90
    };
    
    // Update stats display
    document.getElementById('totalLogs').textContent = stats.totalProjects;
    document.getElementById('activeLogs').textContent = stats.activeLogs.toLocaleString();
    document.getElementById('errorsToday').textContent = stats.errorsToday;
    document.getElementById('systemHealth').textContent = stats.systemHealth + '%';
}

// Setup event listeners
function setupEventListeners() {
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
    
    // Quick action buttons
    const quickActionBtns = document.querySelectorAll('.btn');
    quickActionBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            // Add click animation
            this.style.transform = 'scale(0.95)';
            setTimeout(() => {
                this.style.transform = 'scale(1)';
            }, 150);
        });
    });
    
    // System status progress bars animation
    animateProgressBars();
}

// Logout function
function logout() {
    // Clear session data
    sessionStorage.removeItem('isAuthenticated');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('userEmail');
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    
    // Clear local storage if not remembering
    if (!localStorage.getItem('rememberMe')) {
        localStorage.removeItem('userEmail');
    }
    
    // Redirect to login
    window.location.href = 'index.html';
}

// Animate progress bars
function animateProgressBars() {
    const progressBars = document.querySelectorAll('.progress-bar');
    progressBars.forEach(bar => {
        const width = bar.style.width;
        bar.style.width = '0%';
        
        setTimeout(() => {
            bar.style.width = width;
        }, 500);
    });
}

// Real-time log updates simulation
function simulateLogUpdates() {
    const logTable = document.querySelector('#logsTable tbody');
    if (!logTable) return;
    
    const logLevels = ['INFO', 'WARN', 'ERROR'];
    const projects = ['E-commerce App', 'API Gateway', 'User Service'];
    const messages = [
        'Request processed successfully',
        'Database connection established',
        'Cache miss occurred',
        'User authentication successful',
        'API rate limit exceeded',
        'Memory usage high',
        'Network timeout detected'
    ];
    
    setInterval(() => {
        const newLog = createLogEntry(logLevels, projects, messages);
        addLogToTable(newLog, logTable);
    }, 5000); // Add new log every 5 seconds
}

// Create a new log entry
function createLogEntry(levels, projects, messages) {
    return {
        timestamp: new Date().toLocaleTimeString(),
        project: projects[Math.floor(Math.random() * projects.length)],
        level: levels[Math.floor(Math.random() * levels.length)],
        message: messages[Math.floor(Math.random() * messages.length)],
        user: 'System',
        ip: '192.168.1.' + Math.floor(Math.random() * 255)
    };
}

// Add log entry to table
function addLogToTable(log, table) {
    const row = document.createElement('tr');
    row.className = 'fade-in';
    
    const levelClass = log.level === 'ERROR' ? 'bg-danger' : 
                      log.level === 'WARN' ? 'bg-warning' : 'bg-info';
    
    row.innerHTML = `
        <td>${log.timestamp}</td>
        <td>${log.project}</td>
        <td><span class="badge ${levelClass}">${log.level}</span></td>
        <td>${log.message}</td>
        <td>${log.user}</td>
        <td>${log.ip}</td>
        <td>
            <button class="btn btn-sm btn-outline-primary" onclick="viewLogDetails('${log.timestamp}')">
                <i class="fas fa-eye"></i>
            </button>
        </td>
    `;
    
    // Insert at the top
    table.insertBefore(row, table.firstChild);
    
    // Remove old logs if too many
    if (table.children.length > 10) {
        table.removeChild(table.lastChild);
    }
}

// View log details (placeholder function)
function viewLogDetails(timestamp) {
    console.log('Viewing log details for:', timestamp);
    // This would open a modal with detailed log information
}

// Start log simulation when page loads
setTimeout(simulateLogUpdates, 2000); 