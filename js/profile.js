// Profile page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuthentication();
    
    // Initialize profile
    initializeProfile();
    
    // Setup event listeners
    setupEventListeners();
});

// Check if user is authenticated
function checkAuthentication() {
    const isAuthenticated = localStorage.getItem('isLoggedIn') === 'true' || sessionStorage.getItem('isAuthenticated') === 'true';
    if (!isAuthenticated) {
        window.location.href = 'index.html';
        return;
    }
}

// Initialize profile
function initializeProfile() {
    loadUserData();
    setupProfilePicture();
}

// Load user data
function loadUserData() {
    // Get user data from storage
    const userData = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('userData') || '{}');
    
    // Populate form fields
    if (userData.name) {
        const nameParts = userData.name.split(' ');
        document.getElementById('firstName').value = nameParts[0] || '';
        document.getElementById('lastName').value = nameParts.slice(1).join(' ') || '';
    }
    
    if (userData.email) {
        document.getElementById('email').value = userData.email;
    }
    
    // Update profile display
    updateProfileDisplay(userData);
}

// Update profile display
function updateProfileDisplay(userData) {
    const profilePicture = document.getElementById('profilePicture');
    const profileName = document.querySelector('.card-title');
    
    if (userData.name) {
        // Update profile picture with initials
        const initials = userData.name.split(' ').map(n => n[0]).join('').toUpperCase();
        profilePicture.src = `https://via.placeholder.com/150x150/007bff/ffffff?text=${initials}`;
        
        // Update name display
        if (profileName) {
            profileName.textContent = userData.name;
        }
    }
}

// Setup profile picture
function setupProfilePicture() {
    const profilePictureInput = document.getElementById('profilePictureInput');
    const profilePicture = document.getElementById('profilePicture');
    
    if (profilePictureInput) {
        profilePictureInput.addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                if (file.type.startsWith('image/')) {
                    const reader = new FileReader();
                    reader.onload = function(e) {
                        profilePicture.src = e.target.result;
                        showAlert('Profile picture updated successfully!', 'success');
                    };
                    reader.readAsDataURL(file);
                } else {
                    showAlert('Please select a valid image file.', 'danger');
                }
            }
        });
    }
}

// Setup event listeners
function setupEventListeners() {
    // Profile form
    const profileForm = document.getElementById('profileForm');
    if (profileForm) {
        profileForm.addEventListener('submit', handleProfileUpdate);
    }
    
    // Preferences form
    const preferencesForm = document.getElementById('preferencesForm');
    if (preferencesForm) {
        preferencesForm.addEventListener('submit', handlePreferencesUpdate);
    }
    
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
    
    // Export data button
    const exportDataBtn = document.getElementById('exportDataBtn');
    if (exportDataBtn) {
        exportDataBtn.addEventListener('click', exportUserData);
    }
    
    // Delete account button
    const deleteAccountBtn = document.getElementById('deleteAccountBtn');
    if (deleteAccountBtn) {
        deleteAccountBtn.addEventListener('click', function() {
            const modal = new bootstrap.Modal(document.getElementById('deleteAccountModal'));
            modal.show();
        });
    }
    
    // Confirm delete account
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const confirmDeleteInput = document.getElementById('confirmDelete');
    
    if (confirmDeleteBtn && confirmDeleteInput) {
        confirmDeleteInput.addEventListener('input', function() {
            confirmDeleteBtn.disabled = this.value !== 'DELETE';
        });
        
        confirmDeleteBtn.addEventListener('click', deleteAccount);
    }
}

// Handle profile update
function handleProfileUpdate(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const profileData = {
        firstName: formData.get('firstName'),
        lastName: formData.get('lastName'),
        email: formData.get('email'),
        phone: formData.get('phone'),
        jobTitle: formData.get('jobTitle'),
        department: formData.get('department'),
        bio: formData.get('bio')
    };
    
    // Validate form
    if (!validateProfileForm(profileData)) {
        return;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Updating...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Update storage
        const userData = JSON.parse(localStorage.getItem('currentUser') || sessionStorage.getItem('userData') || '{}');
        userData.name = `${profileData.firstName} ${profileData.lastName}`;
        userData.email = profileData.email;
        localStorage.setItem('currentUser', JSON.stringify(userData));
        sessionStorage.setItem('userData', JSON.stringify(userData));
        
        // Update profile display
        updateProfileDisplay(userData);
        
        // Show success message
        showAlert('Profile updated successfully!', 'success');
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 1500);
}

// Handle preferences update
function handlePreferencesUpdate(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const preferencesData = {
        timezone: formData.get('timezone'),
        language: formData.get('language'),
        dateFormat: formData.get('dateFormat'),
        emailNotifications: formData.get('emailNotifications') === 'on',
        dashboardAlerts: formData.get('dashboardAlerts') === 'on'
    };
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Saving...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Store preferences in localStorage
        localStorage.setItem('userPreferences', JSON.stringify(preferencesData));
        
        // Show success message
        showAlert('Preferences saved successfully!', 'success');
        
        submitBtn.innerHTML = originalText;
        submitBtn.disabled = false;
    }, 1000);
}

// Validate profile form
function validateProfileForm(data) {
    if (!data.firstName || !data.lastName) {
        showAlert('First name and last name are required!', 'danger');
        return false;
    }
    
    if (!data.email) {
        showAlert('Email address is required!', 'danger');
        return false;
    }
    
    if (!isValidEmail(data.email)) {
        showAlert('Please enter a valid email address!', 'danger');
        return false;
    }
    
    return true;
}

// Email validation
function isValidEmail(email) {
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    return emailRegex.test(email);
}

// Export user data
function exportUserData() {
    const exportBtn = document.getElementById('exportDataBtn');
    const originalText = exportBtn.innerHTML;
    exportBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Exporting...';
    exportBtn.disabled = true;
    
    setTimeout(() => {
        // Get user data
        const userData = JSON.parse(sessionStorage.getItem('userData') || '{}');
        const preferences = JSON.parse(localStorage.getItem('userPreferences') || '{}');
        
        // Create export data
        const exportData = {
            user: userData,
            preferences: preferences,
            exportDate: new Date().toISOString()
        };
        
        // Create and download JSON file
        const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.href = url;
        a.download = 'user-data-' + new Date().toISOString().split('T')[0] + '.json';
        a.click();
        window.URL.revokeObjectURL(url);
        
        exportBtn.innerHTML = originalText;
        exportBtn.disabled = false;
        
        showAlert('User data exported successfully!', 'success');
    }, 2000);
}

// Delete account
function deleteAccount() {
    const confirmDeleteBtn = document.getElementById('confirmDeleteBtn');
    const originalText = confirmDeleteBtn.innerHTML;
    confirmDeleteBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Deleting...';
    confirmDeleteBtn.disabled = true;
    
    setTimeout(() => {
        // Clear all data
        sessionStorage.clear();
        localStorage.clear();
        
        // Close modal
        const modal = bootstrap.Modal.getInstance(document.getElementById('deleteAccountModal'));
        modal.hide();
        
        // Show success message
        showAlert('Account deleted successfully. Redirecting to login...', 'success');
        
        // Redirect to login after a delay
        setTimeout(() => {
            window.location.href = 'index.html';
        }, 2000);
    }, 2000);
}

// Show alert message
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Logout function
function logout() {
    // Clear session data
    sessionStorage.removeItem('isAuthenticated');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('userEmail');
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    
    // Clear local storage if not remembering
    if (!localStorage.getItem('rememberMe')) {
        localStorage.removeItem('userEmail');
    }
    
    // Redirect to login
    window.location.href = 'index.html';
} 