// Change password page functionality
document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuthentication();
    
    // Initialize password change
    initializePasswordChange();
    
    // Setup event listeners
    setupEventListeners();
});

// Check if user is authenticated
function checkAuthentication() {
    const isAuthenticated = localStorage.getItem('isLoggedIn') === 'true' || sessionStorage.getItem('isAuthenticated') === 'true';
    if (!isAuthenticated) {
        window.location.href = 'index.html';
        return;
    }
}

// Initialize password change
function initializePasswordChange() {
    setupPasswordToggles();
    setupPasswordValidation();
}

// Setup password visibility toggles
function setupPasswordToggles() {
    const toggles = [
        { button: 'toggleCurrentPassword', input: 'currentPassword' },
        { button: 'toggleNewPassword', input: 'newPassword' },
        { button: 'toggleConfirmPassword', input: 'confirmPassword' }
    ];
    
    toggles.forEach(toggle => {
        const button = document.getElementById(toggle.button);
        const input = document.getElementById(toggle.input);
        
        if (button && input) {
            button.addEventListener('click', function() {
                const type = input.getAttribute('type') === 'password' ? 'text' : 'password';
                input.setAttribute('type', type);
                
                const icon = button.querySelector('i');
                icon.classList.toggle('fa-eye');
                icon.classList.toggle('fa-eye-slash');
            });
        }
    });
}

// Setup password validation
function setupPasswordValidation() {
    const newPasswordInput = document.getElementById('newPassword');
    const confirmPasswordInput = document.getElementById('confirmPassword');
    const submitBtn = document.getElementById('submitBtn');
    
    if (newPasswordInput) {
        newPasswordInput.addEventListener('input', function() {
            validatePassword(this.value);
            checkPasswordMatch();
            updateSubmitButton();
        });
    }
    
    if (confirmPasswordInput) {
        confirmPasswordInput.addEventListener('input', function() {
            checkPasswordMatch();
            updateSubmitButton();
        });
    }
}

// Validate password strength
function validatePassword(password) {
    const requirements = {
        length: password.length >= 8,
        uppercase: /[A-Z]/.test(password),
        lowercase: /[a-z]/.test(password),
        number: /\d/.test(password),
        special: /[!@#$%^&*(),.?":{}|<>]/.test(password)
    };
    
    // Update requirement indicators
    document.getElementById('reqLength').className = requirements.length ? 'text-success' : 'text-danger';
    document.getElementById('reqLength').innerHTML = requirements.length ? 
        '<i class="fas fa-check me-1"></i>At least 8 characters' : 
        '<i class="fas fa-times me-1"></i>At least 8 characters';
    
    document.getElementById('reqUppercase').className = requirements.uppercase ? 'text-success' : 'text-danger';
    document.getElementById('reqUppercase').innerHTML = requirements.uppercase ? 
        '<i class="fas fa-check me-1"></i>At least one uppercase letter' : 
        '<i class="fas fa-times me-1"></i>At least one uppercase letter';
    
    document.getElementById('reqLowercase').className = requirements.lowercase ? 'text-success' : 'text-danger';
    document.getElementById('reqLowercase').innerHTML = requirements.lowercase ? 
        '<i class="fas fa-check me-1"></i>At least one lowercase letter' : 
        '<i class="fas fa-times me-1"></i>At least one lowercase letter';
    
    document.getElementById('reqNumber').className = requirements.number ? 'text-success' : 'text-danger';
    document.getElementById('reqNumber').innerHTML = requirements.number ? 
        '<i class="fas fa-check me-1"></i>At least one number' : 
        '<i class="fas fa-times me-1"></i>At least one number';
    
    document.getElementById('reqSpecial').className = requirements.special ? 'text-success' : 'text-danger';
    document.getElementById('reqSpecial').innerHTML = requirements.special ? 
        '<i class="fas fa-check me-1"></i>At least one special character' : 
        '<i class="fas fa-times me-1"></i>At least one special character';
    
    // Calculate password strength
    const strength = calculatePasswordStrength(requirements);
    updatePasswordStrengthIndicator(strength);
    
    return Object.values(requirements).every(req => req);
}

// Calculate password strength
function calculatePasswordStrength(requirements) {
    const metRequirements = Object.values(requirements).filter(req => req).length;
    
    if (metRequirements < 3) return { level: 'weak', percentage: 25, color: '#dc3545' };
    if (metRequirements < 4) return { level: 'fair', percentage: 50, color: '#ffc107' };
    if (metRequirements < 5) return { level: 'good', percentage: 75, color: '#17a2b8' };
    return { level: 'strong', percentage: 100, color: '#28a745' };
}

// Update password strength indicator
function updatePasswordStrengthIndicator(strength) {
    const strengthBar = document.getElementById('passwordStrengthBar');
    const strengthText = document.getElementById('passwordStrengthText');
    
    if (strengthBar && strengthText) {
        strengthBar.style.width = strength.percentage + '%';
        strengthBar.style.backgroundColor = strength.color;
        strengthText.textContent = `Password strength: ${strength.level.charAt(0).toUpperCase() + strength.level.slice(1)}`;
        strengthText.className = strength.level === 'weak' ? 'text-danger' : 
                                 strength.level === 'fair' ? 'text-warning' : 
                                 strength.level === 'good' ? 'text-info' : 'text-success';
    }
}

// Check if passwords match
function checkPasswordMatch() {
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const matchIndicator = document.getElementById('passwordMatch');
    const matchSuccess = document.getElementById('passwordMatchSuccess');
    
    if (confirmPassword.length === 0) {
        matchIndicator.style.display = 'none';
        matchSuccess.style.display = 'none';
        return false;
    }
    
    if (newPassword === confirmPassword) {
        matchIndicator.style.display = 'none';
        matchSuccess.style.display = 'block';
        return true;
    } else {
        matchIndicator.style.display = 'block';
        matchSuccess.style.display = 'none';
        return false;
    }
}

// Update submit button state
function updateSubmitButton() {
    const submitBtn = document.getElementById('submitBtn');
    const newPassword = document.getElementById('newPassword').value;
    const confirmPassword = document.getElementById('confirmPassword').value;
    const currentPassword = document.getElementById('currentPassword').value;
    
    const isPasswordValid = validatePassword(newPassword);
    const doPasswordsMatch = checkPasswordMatch();
    const hasCurrentPassword = currentPassword.length > 0;
    
    submitBtn.disabled = !(isPasswordValid && doPasswordsMatch && hasCurrentPassword);
}

// Setup event listeners
function setupEventListeners() {
    // Change password form
    const changePasswordForm = document.getElementById('changePasswordForm');
    if (changePasswordForm) {
        changePasswordForm.addEventListener('submit', handlePasswordChange);
    }
    
    // Logout functionality
    const logoutBtn = document.getElementById('logoutBtn');
    if (logoutBtn) {
        logoutBtn.addEventListener('click', function(e) {
            e.preventDefault();
            logout();
        });
    }
    
    // Redirect to login button
    const redirectToLoginBtn = document.getElementById('redirectToLogin');
    if (redirectToLoginBtn) {
        redirectToLoginBtn.addEventListener('click', function() {
            window.location.href = 'index.html';
        });
    }
    
    // Add input event listeners for real-time validation
    const currentPasswordInput = document.getElementById('currentPassword');
    if (currentPasswordInput) {
        currentPasswordInput.addEventListener('input', updateSubmitButton);
    }
}

// Handle password change
function handlePasswordChange(e) {
    e.preventDefault();
    
    const formData = new FormData(e.target);
    const passwordData = {
        currentPassword: formData.get('currentPassword'),
        newPassword: formData.get('newPassword'),
        confirmPassword: formData.get('confirmPassword'),
        logoutOtherDevices: formData.get('logoutOtherDevices') === 'on'
    };
    
    // Validate form
    if (!validatePasswordChange(passwordData)) {
        return;
    }
    
    // Show loading state
    const submitBtn = e.target.querySelector('button[type="submit"]');
    const originalText = submitBtn.innerHTML;
    submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Changing Password...';
    submitBtn.disabled = true;
    
    // Simulate API call
    setTimeout(() => {
        // Mock password change - replace with actual API call
        if (passwordData.currentPassword === 'password123') {
            // Show success modal
            const successModal = new bootstrap.Modal(document.getElementById('successModal'));
            successModal.show();
            
            // Clear form
            e.target.reset();
            
            // Clear session if logout other devices is selected
            if (passwordData.logoutOtherDevices) {
                sessionStorage.removeItem('isAuthenticated');
                sessionStorage.removeItem('userData');
                sessionStorage.removeItem('userEmail');
            }
        } else {
            showAlert('Current password is incorrect. Please try again.', 'danger');
            submitBtn.innerHTML = originalText;
            submitBtn.disabled = false;
        }
    }, 2000);
}

// Validate password change form
function validatePasswordChange(data) {
    if (!data.currentPassword) {
        showAlert('Please enter your current password.', 'danger');
        return false;
    }
    
    if (!data.newPassword) {
        showAlert('Please enter a new password.', 'danger');
        return false;
    }
    
    if (!validatePassword(data.newPassword)) {
        showAlert('Please ensure your new password meets all requirements.', 'danger');
        return false;
    }
    
    if (data.newPassword !== data.confirmPassword) {
        showAlert('New passwords do not match.', 'danger');
        return false;
    }
    
    if (data.currentPassword === data.newPassword) {
        showAlert('New password must be different from current password.', 'danger');
        return false;
    }
    
    return true;
}

// Show alert message
function showAlert(message, type) {
    const alertDiv = document.createElement('div');
    alertDiv.className = `alert alert-${type} alert-dismissible fade show position-fixed`;
    alertDiv.style.cssText = 'top: 20px; right: 20px; z-index: 9999; min-width: 300px;';
    alertDiv.innerHTML = `
        ${message}
        <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
    `;
    
    document.body.appendChild(alertDiv);
    
    // Auto-remove after 5 seconds
    setTimeout(() => {
        if (alertDiv.parentNode) {
            alertDiv.remove();
        }
    }, 5000);
}

// Logout function
function logout() {
    // Clear session data
    sessionStorage.removeItem('isAuthenticated');
    sessionStorage.removeItem('userData');
    sessionStorage.removeItem('userEmail');
    localStorage.removeItem('isLoggedIn');
    localStorage.removeItem('currentUser');
    
    // Clear local storage if not remembering
    if (!localStorage.getItem('rememberMe')) {
        localStorage.removeItem('userEmail');
    }
    
    // Redirect to login
    window.location.href = 'index.html';
} 